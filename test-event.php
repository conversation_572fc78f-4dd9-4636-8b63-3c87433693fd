<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Event Firing...\n\n";

// Get first user and post for testing
$user = App\Models\User::first();
$post = App\Models\Post::first();

if ($user && $post) {
    echo "Testing with User: {$user->name} and Post ID: {$post->id}\n";

    // Find or create a test reaction
    $reaction = App\Models\Reaction::where([
        'reactable_type' => 'App\Models\Post',
        'reactable_id' => $post->id,
        'user_id' => $user->id,
    ])->first();

    if (!$reaction) {
        $reaction = new App\Models\Reaction([
            'reactable_type' => 'App\Models\Post',
            'reactable_id' => $post->id,
            'user_id' => $user->id,
            'type' => 'like'
        ]);
        $reaction->save();
        echo "✅ New reaction created with ID: {$reaction->id}\n";
    } else {
        echo "✅ Using existing reaction with ID: {$reaction->id}\n";
    }

    // Fire the event manually
    event(new App\Events\PostReactionAdded($user, $post, $reaction));
    echo "✅ PostReactionAdded event fired!\n";

    // Check if job was queued
    $jobCount = \Illuminate\Support\Facades\DB::table('jobs')->count();
    echo "📋 Jobs in queue: $jobCount\n";
    
} else {
    echo "❌ No users or posts found for testing\n";
    echo "Users count: " . App\Models\User::count() . "\n";
    echo "Posts count: " . App\Models\Post::count() . "\n";
}

echo "\n🎯 Check the queue worker terminal to see if the job is being processed!\n";
