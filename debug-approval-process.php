<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Approval Process\n";
echo "=============================\n\n";

$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

echo "Test Setup:\n";
echo "- Author: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Admin: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n\n";

// Create multiple test posts to see the pattern
$testPosts = [];
for ($i = 1; $i <= 5; $i++) {
    $post = App\Models\Post::create([
        'title' => "Approval Test {$i} - " . now()->format('H:i:s'),
        'content' => "Testing approval process consistency - post {$i}.",
        'user_id' => $arjohn->id,
        'group_id' => $psits->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'published_at' => now(),
    ]);
    $testPosts[] = $post;
    echo "Created test post {$i}: \"{$post->title}\" (ID: {$post->id})\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

// Count notifications before all approvals
$notificationsBefore = $arjohn->notifications()->count();
echo "Arjohn's notifications before all approvals: {$notificationsBefore}\n\n";

// Authenticate as Jayvee
auth()->login($jayvee);
echo "✅ Authenticated as {$jayvee->name}\n\n";

$successfulNotifications = 0;
$controller = new App\Http\Controllers\GroupController();

foreach ($testPosts as $index => $post) {
    $postNum = $index + 1;
    echo "=== APPROVING POST {$postNum} ===\n";
    echo "Post: \"{$post->title}\" (ID: {$post->id})\n";
    
    // Count notifications before this approval
    $notificationsBefore_single = $arjohn->notifications()->count();
    echo "Notifications before: {$notificationsBefore_single}\n";
    
    try {
        // Call the approval method
        $response = $controller->approvePost($psits, $post);
        echo "✅ Approval method executed\n";
        
        // Check post status
        $post->refresh();
        echo "Post status: {$post->approval_status}\n";
        echo "Approved at: {$post->approved_at}\n";
        echo "Approved by: {$post->approved_by}\n";
        
        // Wait for notification processing
        sleep(1);
        
        // Check notifications after
        $notificationsAfter_single = $arjohn->notifications()->count();
        echo "Notifications after: {$notificationsAfter_single}\n";
        
        $increase = $notificationsAfter_single - $notificationsBefore_single;
        echo "Notification increase: {$increase}\n";
        
        if ($increase > 0) {
            echo "✅ SUCCESS: Notification created\n";
            $successfulNotifications++;
            
            // Get the notification
            $notification = $arjohn->notifications()
                ->where('type', 'App\Notifications\GroupPostApproved')
                ->latest()
                ->first();
            
            if ($notification && isset($notification->data['post_id']) && $notification->data['post_id'] == $post->id) {
                echo "✅ Correct notification found\n";
            } else {
                echo "❌ Notification found but doesn't match post\n";
            }
        } else {
            echo "❌ FAILED: No notification created\n";
            
            // Debug this specific failure
            echo "DEBUG INFO:\n";
            echo "- Post user ID: {$post->user_id}\n";
            echo "- Approver ID: {$jayvee->id}\n";
            echo "- Same user: " . ($post->user_id === $jayvee->id ? 'YES' : 'NO') . "\n";
            echo "- User wants notifications: " . ($arjohn->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
            echo "- User notifications enabled: " . ($arjohn->notifications_enabled ? 'YES' : 'NO') . "\n";
            
            // Check if the notification code was even reached
            echo "- Checking if notification should be sent...\n";
            
            if ($post->user && $post->user->id !== $jayvee->id) {
                echo "  * Condition 1 (user exists and not self): PASS\n";
                
                // Manually try to send the notification
                echo "  * Attempting manual notification...\n";
                try {
                    $post->user->notify(new \App\Notifications\GroupPostApproved($post, $psits, $jayvee));
                    echo "  * Manual notification sent successfully\n";
                    
                    sleep(1);
                    $manualCheck = $arjohn->notifications()->count();
                    if ($manualCheck > $notificationsAfter_single) {
                        echo "  * Manual notification verified in database\n";
                    } else {
                        echo "  * Manual notification not found in database\n";
                    }
                } catch (Exception $e) {
                    echo "  * Manual notification failed: " . $e->getMessage() . "\n";
                }
            } else {
                echo "  * Condition 1 (user exists and not self): FAIL\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error during approval: " . $e->getMessage() . "\n";
    }
    
    echo str_repeat("-", 30) . "\n\n";
}

auth()->logout();

// Final summary
$totalNotificationsAfter = $arjohn->notifications()->count();
$totalIncrease = $totalNotificationsAfter - $notificationsBefore;

echo "=== FINAL SUMMARY ===\n";
echo "Posts approved: " . count($testPosts) . "\n";
echo "Successful notifications: {$successfulNotifications}\n";
echo "Success rate: " . round(($successfulNotifications / count($testPosts)) * 100, 1) . "%\n";
echo "Total notification increase: {$totalIncrease}\n";

if ($successfulNotifications < count($testPosts)) {
    echo "❌ INCONSISTENT: Approval notifications are not working reliably\n";
    echo "This confirms the intermittent failure issue.\n";
} else {
    echo "✅ CONSISTENT: All approvals created notifications\n";
}

// Clean up
echo "\nCleaning up test posts...\n";
foreach ($testPosts as $post) {
    $post->delete();
}
echo "✅ All test posts deleted\n";

echo "\n🏁 Approval process debug completed!\n";
