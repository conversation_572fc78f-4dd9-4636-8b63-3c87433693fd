<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Group Post Rejection Notification\n";
echo "==============================================\n\n";

// Get test data
$user = App\Models\User::where('role', 'student')->first();
$admin = App\Models\User::where('role', 'admin')->first();
$group = App\Models\Group::first();

echo "Test Data:\n";
echo "Post Author: {$user->name} (ID: {$user->id})\n";
echo "Admin/Rejector: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Create a test post
$post = App\Models\Post::create([
    'title' => 'Debug Rejection Test - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'Debug test post.',
    'user_id' => $user->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Test notification preferences in detail
echo "Detailed notification preference check:\n";
echo "- notifications_enabled: " . ($user->notifications_enabled ? 'true' : 'false') . "\n";
echo "- notification_preferences: " . json_encode($user->notification_preferences) . "\n";
echo "- getNotificationPreferences(): " . json_encode($user->getNotificationPreferences()) . "\n";
echo "- wantsNotification('group_post_approvals'): " . ($user->wantsNotification('group_post_approvals') ? 'true' : 'false') . "\n\n";

// Test creating the notification directly
echo "Testing GroupPostRejected notification creation:\n";

try {
    $notification = new \App\Notifications\GroupPostRejected($post, $group, $admin);
    echo "✅ Notification object created successfully\n";
    
    // Test the via method
    $channels = $notification->via($user);
    echo "Channels returned by via(): " . json_encode($channels) . "\n";
    
    if (empty($channels)) {
        echo "❌ No channels returned - notification will not be sent!\n";
        echo "This means wantsNotification('group_post_approvals') returned false\n";
    } else {
        echo "✅ Channels available, notification should be sent\n";
        
        // Test the toArray method
        $data = $notification->toArray($user);
        echo "Notification data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
        
        // Actually send the notification
        echo "\nSending notification...\n";
        $user->notify($notification);
        echo "✅ Notification sent\n";
        
        // Check if it was created
        sleep(1);
        $latestNotification = $user->notifications()
            ->where('type', 'App\Notifications\GroupPostRejected')
            ->latest()
            ->first();
            
        if ($latestNotification) {
            echo "✅ Notification found in database!\n";
            echo "Message: {$latestNotification->data['message']}\n";
        } else {
            echo "❌ Notification not found in database\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error creating notification: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
$post->delete();
echo "✅ Test post deleted\n";

echo "\n🏁 Debug completed!\n";
