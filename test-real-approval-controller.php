<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Real Group Post Approval Controller\n";
echo "==============================================\n\n";

// Get test data
$student = App\Models\User::find(2);
$admin = App\Models\User::find(1);
$group = App\Models\Group::find(1);

echo "Test Data:\n";
echo "Student: {$student->name} (ID: {$student->id})\n";
echo "Admin: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Create a test post that needs approval
$testPost = App\Models\Post::create([
    'title' => 'Controller Test Post - ' . now()->format('H:i:s'),
    'content' => 'Testing real controller approval process.',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$testPost->title} (ID: {$testPost->id})\n";
echo "Post approval status: {$testPost->approval_status}\n\n";

// Count notifications before
$notificationsBefore = $student->notifications()->count();
echo "Student notifications before approval: {$notificationsBefore}\n";

// Simulate authentication as admin
auth()->login($admin);
echo "✅ Authenticated as admin: {$admin->name}\n";

// Create controller instance and call the method directly
echo "Calling GroupController::approvePost method...\n";

try {
    $controller = new App\Http\Controllers\GroupController();
    
    // Call the approvePost method directly
    $response = $controller->approvePost($group, $testPost);
    
    echo "✅ Controller method executed successfully\n";
    echo "Response type: " . get_class($response) . "\n";
    
    // Check if post was updated
    $testPost->refresh();
    echo "Post approval status after controller call: {$testPost->approval_status}\n";
    echo "Post approved at: {$testPost->approved_at}\n";
    echo "Post approved by: {$testPost->approved_by}\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications after
    $notificationsAfter = $student->notifications()->count();
    echo "Student notifications after approval: {$notificationsAfter}\n";
    
    $increase = $notificationsAfter - $notificationsBefore;
    echo "Notification increase: {$increase}\n";
    
    if ($increase > 0) {
        echo "✅ SUCCESS: Controller approval created notification!\n";
        
        // Get the latest notification
        $latestNotification = $student->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
        
        if ($latestNotification) {
            echo "\nLatest notification:\n";
            echo "- Type: {$latestNotification->data['type']}\n";
            echo "- Message: {$latestNotification->data['message']}\n";
            echo "- Created: {$latestNotification->created_at}\n";
        }
    } else {
        echo "❌ FAILED: Controller approval did not create notification\n";
        
        // Debug information
        echo "\nDebugging information:\n";
        echo "- Post user ID: {$testPost->user_id}\n";
        echo "- Admin ID: {$admin->id}\n";
        echo "- Same user check: " . ($testPost->user->id === $admin->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
        echo "- Student notification preferences: " . json_encode($student->notification_preferences) . "\n";
        echo "- Student wants group_post_approvals: " . ($student->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error calling controller method: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

// Clean up
echo "\nCleaning up test post...\n";
$testPost->delete();
echo "✅ Test post deleted\n";

// Logout
auth()->logout();

echo "\n🏁 Controller test completed!\n";
