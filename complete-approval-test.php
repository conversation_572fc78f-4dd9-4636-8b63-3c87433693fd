<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Complete Group Post Approval Test\n";
echo "====================================\n\n";

// Get test users
$student = App\Models\User::find(2);
$admin = App\Models\User::find(1);
$group = App\Models\Group::find(1);

echo "Test Setup:\n";
echo "- Student: {$student->name} (ID: {$student->id})\n";
echo "- Admin: {$admin->name} (ID: {$admin->id})\n";
echo "- Group: {$group->name} (ID: {$group->id})\n";
echo "- Group requires approval: " . ($group->post_approval === 'required' ? 'YES' : 'NO') . "\n\n";

// Step 1: Create a post that needs approval
echo "Step 1: Creating a post that needs approval...\n";
$testPost = App\Models\Post::create([
    'title' => 'Complete Test Post - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'This post is created to test the complete approval workflow.',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "✅ Created post: \"{$testPost->title}\" (ID: {$testPost->id})\n";
echo "   Status: {$testPost->approval_status}\n\n";

// Step 2: Count notifications before approval
$notificationsBefore = $student->notifications()->count();
echo "Step 2: Checking notifications before approval...\n";
echo "✅ Student has {$notificationsBefore} notifications\n\n";

// Step 3: Simulate admin approval
echo "Step 3: Admin approving the post...\n";
auth()->login($admin);

// Use the actual controller method
$controller = new App\Http\Controllers\GroupController();
$response = $controller->approvePost($group, $testPost);

echo "✅ Post approved by admin\n";

// Refresh the post to see changes
$testPost->refresh();
echo "   New status: {$testPost->approval_status}\n";
echo "   Approved at: {$testPost->approved_at}\n";
echo "   Approved by: {$testPost->approved_by} ({$admin->name})\n\n";

// Step 4: Check if notification was created
sleep(1); // Give it a moment to process
$notificationsAfter = $student->notifications()->count();
echo "Step 4: Checking notifications after approval...\n";
echo "✅ Student now has {$notificationsAfter} notifications\n";

$increase = $notificationsAfter - $notificationsBefore;
echo "   Notification increase: {$increase}\n";

if ($increase > 0) {
    echo "✅ SUCCESS: Approval notification was created!\n\n";
    
    // Get the notification details
    $approvalNotification = $student->notifications()
        ->where('type', 'App\Notifications\GroupPostApproved')
        ->latest()
        ->first();
    
    if ($approvalNotification) {
        echo "Notification Details:\n";
        echo "- ID: {$approvalNotification->id}\n";
        echo "- Type: {$approvalNotification->data['type']}\n";
        echo "- Message: \"{$approvalNotification->data['message']}\"\n";
        echo "- Created: {$approvalNotification->created_at}\n";
        echo "- Read: " . ($approvalNotification->read_at ? 'YES' : 'NO') . "\n\n";
    }
} else {
    echo "❌ FAILED: No notification was created\n\n";
}

// Step 5: Test rejection as well
echo "Step 5: Testing post rejection...\n";

// Create another test post
$testPost2 = App\Models\Post::create([
    'title' => 'Test Rejection Post - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'This post will be rejected to test rejection notifications.',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "✅ Created second post: \"{$testPost2->title}\" (ID: {$testPost2->id})\n";

$notificationsBefore2 = $student->notifications()->count();

// Reject the post
$response2 = $controller->rejectPost($group, $testPost2);
echo "✅ Post rejected by admin\n";

sleep(1);
$notificationsAfter2 = $student->notifications()->count();
$increase2 = $notificationsAfter2 - $notificationsBefore2;

echo "   Rejection notification increase: {$increase2}\n";

if ($increase2 > 0) {
    echo "✅ SUCCESS: Rejection notification was also created!\n";
    
    $rejectionNotification = $student->notifications()
        ->where('type', 'App\Notifications\GroupPostRejected')
        ->latest()
        ->first();
    
    if ($rejectionNotification) {
        echo "   Message: \"{$rejectionNotification->data['message']}\"\n";
    }
} else {
    echo "❌ FAILED: No rejection notification was created\n";
}

// Clean up
echo "\nCleaning up test posts...\n";
$testPost->delete();
$testPost2->delete();
auth()->logout();
echo "✅ Test posts deleted and admin logged out\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎉 CONCLUSION: Group Post Approval Notifications are WORKING!\n";
echo str_repeat("=", 50) . "\n\n";

echo "How to test in the real application:\n";
echo "1. Log in as a student\n";
echo "2. Create a post in a group that requires approval\n";
echo "3. Log in as an admin/moderator\n";
echo "4. Go to the group's pending posts page\n";
echo "5. Approve or reject the post\n";
echo "6. Log back in as the student\n";
echo "7. Check the notification dropdown - you should see the approval/rejection notification\n\n";

echo "🏁 Complete test finished!\n";
