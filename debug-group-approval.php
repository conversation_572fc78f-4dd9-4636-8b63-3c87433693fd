<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Group Post Approval System\n";
echo "======================================\n\n";

// Get test data
$student = App\Models\User::where('role', 'student')->first();
$admin = App\Models\User::where('role', 'admin')->first();
$group = App\Models\Group::first();

echo "Test Data:\n";
echo "Student: {$student->name} (ID: {$student->id})\n";
echo "Admin: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n";
echo "Group post approval setting: {$group->post_approval}\n\n";

// Check if there are any pending posts
$pendingPosts = App\Models\Post::where('group_id', $group->id)
    ->where('approval_status', 'pending')
    ->get();

echo "Pending posts in group: " . $pendingPosts->count() . "\n";

if ($pendingPosts->count() > 0) {
    echo "Existing pending posts:\n";
    foreach ($pendingPosts as $post) {
        echo "- Post ID: {$post->id}, Title: {$post->title}, Author: {$post->user->name}\n";
    }
} else {
    echo "No pending posts found. Creating a test post...\n";
    
    // Create a test post that needs approval
    $testPost = App\Models\Post::create([
        'title' => 'Test Post for Approval - ' . now()->format('Y-m-d H:i:s'),
        'content' => 'This is a test post to verify approval notifications.',
        'user_id' => $student->id,
        'group_id' => $group->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'published_at' => now(),
    ]);
    
    echo "✅ Created test post: {$testPost->title} (ID: {$testPost->id})\n";
    $pendingPosts = collect([$testPost]);
}

// Test the approval process with the first pending post
$testPost = $pendingPosts->first();
echo "\nTesting approval process with post: {$testPost->title} (ID: {$testPost->id})\n";
echo "Post author: {$testPost->user->name} (ID: {$testPost->user_id})\n";
echo "Post approval status: {$testPost->approval_status}\n";

// Count notifications before approval
$notificationsBefore = $testPost->user->notifications()->count();
echo "Author notifications before approval: {$notificationsBefore}\n";

// Check notification preferences
echo "Author wants group_post_approvals: " . ($testPost->user->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";

// Check if admin can moderate
echo "Admin can moderate group: " . ($group->userCanModerate($admin) ? 'YES' : 'NO') . "\n";

// Simulate the approval process step by step
echo "\n--- Simulating Approval Process ---\n";

// Step 1: Update post status
echo "1. Updating post approval status...\n";
$testPost->update([
    'approval_status' => 'approved',
    'approved_at' => now(),
    'approved_by' => $admin->id,
]);
echo "✅ Post status updated to: {$testPost->fresh()->approval_status}\n";

// Step 2: Check conditions for notification
echo "2. Checking notification conditions...\n";
echo "- Post user exists: " . ($testPost->user ? 'YES' : 'NO') . "\n";
echo "- Different user (not self-approval): " . ($testPost->user->id !== $admin->id ? 'YES' : 'NO') . "\n";

// Step 3: Try to send notification
if ($testPost->user && $testPost->user->id !== $admin->id) {
    echo "3. Attempting to send GroupPostApproved notification...\n";
    
    try {
        // Create notification instance
        $notification = new \App\Notifications\GroupPostApproved($testPost, $group, $admin);
        echo "✅ Notification instance created\n";
        
        // Check via method
        $channels = $notification->via($testPost->user);
        echo "Notification channels: " . implode(', ', $channels) . "\n";
        
        if (in_array('database', $channels)) {
            echo "✅ Database channel is enabled\n";
            
            // Send the notification
            $testPost->user->notify($notification);
            echo "✅ Notification sent\n";
            
            // Wait a moment for processing
            sleep(1);
            
            // Check if notification was created
            $notificationsAfter = $testPost->user->notifications()->count();
            echo "Author notifications after approval: {$notificationsAfter}\n";
            
            $increase = $notificationsAfter - $notificationsBefore;
            echo "Notification increase: {$increase}\n";
            
            if ($increase > 0) {
                echo "✅ SUCCESS: Approval notification was created!\n";
                
                // Get the latest notification
                $latestNotification = $testPost->user->notifications()
                    ->where('type', 'App\Notifications\GroupPostApproved')
                    ->latest()
                    ->first();
                
                if ($latestNotification) {
                    echo "\nLatest notification details:\n";
                    echo "- ID: {$latestNotification->id}\n";
                    echo "- Type: {$latestNotification->data['type']}\n";
                    echo "- Message: {$latestNotification->data['message']}\n";
                    echo "- Created: {$latestNotification->created_at}\n";
                }
            } else {
                echo "❌ FAILED: No notification was created\n";
            }
        } else {
            echo "❌ Database channel is not enabled\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error sending notification: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "❌ Notification conditions not met\n";
}

echo "\n🏁 Debug completed!\n";
