<?php

namespace App\Events;

use App\Models\User;
use App\Models\Post;
use App\Models\Reaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostReactionAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $post;
    public $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Post $post, Reaction $reaction)
    {
        $this->user = $user;
        $this->post = $post;
        $this->reaction = $reaction;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.User.' . $this->post->user_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => uniqid(),
            'type' => 'post_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        $reactionLabel = $this->getReactionLabel($this->reaction->type);
        return "{$this->user->name} reacted {$reactionLabel} to your post";
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        if ($this->post->group_id) {
            return route('groups.show', $this->post->group->slug) . '#post-' . $this->post->id;
        } elseif ($this->post->organization_id) {
            return route('organizations.show', $this->post->organization->slug) . '#post-' . $this->post->id;
        } else {
            return route('profile.user', $this->post->user) . '#post-' . $this->post->id;
        }
    }

    /**
     * Get reaction emoji
     */
    private function getReactionEmoji(string $type): string
    {
        $emojis = [
            'like' => '👍',
            'love' => '❤️',
            'haha' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];

        return $emojis[$type] ?? '👍';
    }

    /**
     * Get reaction label
     */
    private function getReactionLabel(string $type): string
    {
        $labels = [
            'like' => 'with a like',
            'love' => 'with love',
            'haha' => 'with laughter',
            'wow' => 'with wow',
            'sad' => 'with sadness',
            'angry' => 'with anger',
        ];

        return $labels[$type] ?? 'with a like';
    }
}
