<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing REAL Group Post Workflow\n";
echo "===================================\n\n";

// Get users
$arjohn = App\Models\User::find(7); // Ar<PERSON>hn Egamen (post author)
$jayvee = App\Models\User::find(2); // Jayvee <PERSON> Cordova (PSITS admin)
$psits = App\Models\Group::find(3); // PSITS group

echo "Test Setup:\n";
echo "- Post Author: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Group Admin: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n";
echo "- Group post approval setting: {$psits->post_approval}\n\n";

// Check if Arjohn is a member of the group
$membership = \Illuminate\Support\Facades\DB::table('group_members')
    ->where('group_id', $psits->id)
    ->where('user_id', $arjohn->id)
    ->first();

if ($membership) {
    echo "✅ {$arjohn->name} is a member of {$psits->name} (role: {$membership->role}, status: {$membership->status})\n";
} else {
    echo "❌ {$arjohn->name} is NOT a member of {$psits->name}\n";
    echo "Adding {$arjohn->name} to the group...\n";
    
    // Add Arjohn to the group
    $psits->members()->attach($arjohn->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_at' => now(),
    ]);
    echo "✅ {$arjohn->name} added to {$psits->name}\n";
}

// Count notifications before
$notificationsBefore = $arjohn->notifications()->count();
echo "Arjohn's notifications before: {$notificationsBefore}\n\n";

// Step 1: Create post through the REAL PostController workflow
echo "=== STEP 1: Creating Post via PostController ===\n";
auth()->login($arjohn);
echo "✅ Authenticated as {$arjohn->name}\n";

// Simulate the real post creation request
$postData = [
    'title' => 'Real Workflow Test - ' . now()->format('H:i:s'),
    'content' => 'This post is created through the real PostController workflow.',
    'type' => 'general',
    'group_id' => $psits->id,
    'status' => 'published',
];

echo "Creating post through PostController::store()...\n";

try {
    // Create a mock request
    $request = new \Illuminate\Http\Request();
    $request->merge($postData);
    $request->setMethod('POST');
    
    // Use the actual PostController
    $postController = new App\Http\Controllers\PostController();
    
    // Call the store method
    $response = $postController->store($request);
    
    echo "✅ PostController::store() executed successfully\n";
    
    // Find the created post
    $createdPost = App\Models\Post::where('title', $postData['title'])
        ->where('user_id', $arjohn->id)
        ->where('group_id', $psits->id)
        ->latest()
        ->first();
    
    if ($createdPost) {
        echo "✅ Post created: \"{$createdPost->title}\" (ID: {$createdPost->id})\n";
        echo "Post approval status: {$createdPost->approval_status}\n";
        echo "Post status: {$createdPost->status}\n";
        echo "Published at: {$createdPost->published_at}\n";
        
        if ($createdPost->approval_status === 'pending') {
            echo "✅ Post correctly set to pending approval\n\n";
            
            // Step 2: Approve the post through the real GroupController workflow
            echo "=== STEP 2: Approving Post via GroupController ===\n";
            auth()->logout();
            auth()->login($jayvee);
            echo "✅ Authenticated as {$jayvee->name} (group admin)\n";
            
            echo "Approving post through GroupController::approvePost()...\n";
            
            try {
                $groupController = new App\Http\Controllers\GroupController();
                $approvalResponse = $groupController->approvePost($psits, $createdPost);
                
                echo "✅ GroupController::approvePost() executed successfully\n";
                
                // Check post status after approval
                $createdPost->refresh();
                echo "Post approval status after: {$createdPost->approval_status}\n";
                echo "Approved at: {$createdPost->approved_at}\n";
                echo "Approved by: {$createdPost->approved_by}\n";
                
                // Wait for notification processing
                sleep(2);
                
                // Check notifications
                $notificationsAfter = $arjohn->notifications()->count();
                echo "Arjohn's notifications after: {$notificationsAfter}\n";
                
                $increase = $notificationsAfter - $notificationsBefore;
                echo "Notification increase: {$increase}\n";
                
                if ($increase > 0) {
                    echo "✅ SUCCESS: Real workflow approval notification created!\n";
                    
                    // Get the notification details
                    $approvalNotification = $arjohn->notifications()
                        ->where('type', 'App\Notifications\GroupPostApproved')
                        ->latest()
                        ->first();
                    
                    if ($approvalNotification) {
                        echo "\nNotification Details:\n";
                        echo "- Message: \"{$approvalNotification->data['message']}\"\n";
                        echo "- Approved by: {$approvalNotification->data['user_name']}\n";
                        echo "- Group: {$approvalNotification->data['group_name']}\n";
                        echo "- Post title: {$approvalNotification->data['post_title']}\n";
                        echo "- Created: {$approvalNotification->created_at}\n";
                    }
                } else {
                    echo "❌ FAILED: No notification created in real workflow\n";
                    
                    // Debug the approval process
                    echo "\nDEBUG INFO:\n";
                    echo "- Post user ID: {$createdPost->user_id}\n";
                    echo "- Approver ID: {$jayvee->id}\n";
                    echo "- Same user check: " . ($createdPost->user_id === $jayvee->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
                    echo "- Author wants group_post_approvals: " . ($arjohn->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
                    echo "- Author notifications enabled: " . ($arjohn->notifications_enabled ? 'YES' : 'NO') . "\n";
                    
                    // Check if the notification was created but not counted
                    $allNotifications = $arjohn->notifications()
                        ->where('created_at', '>=', now()->subMinutes(5))
                        ->get();
                    
                    echo "- Recent notifications (last 5 min): {$allNotifications->count()}\n";
                    foreach ($allNotifications as $notif) {
                        echo "  * {$notif->type}: {$notif->data['message']}\n";
                    }
                }
                
            } catch (Exception $e) {
                echo "❌ Error during approval: " . $e->getMessage() . "\n";
            }
            
        } else {
            echo "❌ Post was not set to pending - approval not required or user is admin\n";
        }
        
        // Clean up
        echo "\nCleaning up...\n";
        $createdPost->delete();
        echo "✅ Test post deleted\n";
        
    } else {
        echo "❌ Could not find the created post\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during post creation: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

auth()->logout();

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 REAL WORKFLOW TEST RESULT:\n";
echo "The group post approval notification system ";
echo (isset($increase) && $increase > 0 ? "IS WORKING!" : "NEEDS INVESTIGATION");
echo "\n" . str_repeat("=", 50) . "\n";

echo "\n🏁 Real workflow test completed!\n";
