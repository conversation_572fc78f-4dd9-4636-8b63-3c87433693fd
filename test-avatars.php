<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing User Avatars...\n\n";

// Get all users and check their avatars
$users = App\Models\User::all();

echo "Total users: " . $users->count() . "\n\n";

foreach ($users as $user) {
    echo "User: {$user->name} (ID: {$user->id})\n";
    echo "  Avatar field: " . ($user->avatar ? $user->avatar : 'NULL') . "\n";
    echo "  Has real avatar: " . ($user->hasRealProfilePicture() ? 'YES' : 'NO') . "\n";
    echo "  Avatar URL: " . $user->getAvatarUrl() . "\n";
    echo "  Notification Avatar URL: " . $user->getNotificationAvatarUrl() . "\n";
    
    if ($user->avatar) {
        $fullPath = storage_path('app/public/' . $user->avatar);
        echo "  File exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
        if (file_exists($fullPath)) {
            echo "  File size: " . filesize($fullPath) . " bytes\n";
        }
    }
    echo "\n";
}

// Check recent notifications to see what avatars are being used
echo "Recent notifications with avatars:\n";
$recentNotifications = \Illuminate\Support\Facades\DB::table('notifications')
    ->orderBy('created_at', 'desc')
    ->limit(5)
    ->get();

foreach ($recentNotifications as $notification) {
    $data = json_decode($notification->data, true);
    if (isset($data['user_avatar'])) {
        echo "  Notification: {$data['type']} - Avatar: {$data['user_avatar']}\n";
    }
}
