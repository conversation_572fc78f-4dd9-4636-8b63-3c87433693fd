<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Checking Real Approval Notifications\n";
echo "=======================================\n\n";

$arjohn = App\Models\User::find(7); // Arjohn Egamen

echo "Checking notifications for: {$arjohn->name} (ID: {$arjohn->id})\n\n";

// Get recent posts that were approved/rejected
$recentPosts = App\Models\Post::where('user_id', $arjohn->id)
    ->where('group_id', 3) // PSITS
    ->whereIn('approval_status', ['approved', 'rejected'])
    ->where('created_at', '>=', now()->subHours(2))
    ->orderBy('created_at', 'desc')
    ->get();

echo "Recent approved/rejected posts (last 2 hours): {$recentPosts->count()}\n";
foreach ($recentPosts as $post) {
    echo "- Post ID: {$post->id}, Title: \"{$post->title}\", Status: {$post->approval_status}\n";
    echo "  Created: {$post->created_at}\n";
    echo "  Approved at: {$post->approved_at}\n";
    echo "  Approved by: {$post->approved_by}\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// Check for corresponding notifications
$recentNotifications = $arjohn->notifications()
    ->whereIn('type', ['App\Notifications\GroupPostApproved', 'App\Notifications\GroupPostRejected'])
    ->where('created_at', '>=', now()->subHours(2))
    ->orderBy('created_at', 'desc')
    ->get();

echo "Recent approval/rejection notifications (last 2 hours): {$recentNotifications->count()}\n";

if ($recentNotifications->count() > 0) {
    foreach ($recentNotifications as $notification) {
        echo "- Type: " . class_basename($notification->type) . "\n";
        echo "  Message: \"{$notification->data['message']}\"\n";
        echo "  Post ID: {$notification->data['post_id']}\n";
        echo "  Post Title: {$notification->data['post_title']}\n";
        echo "  Approved by: {$notification->data['user_name']}\n";
        echo "  Created: {$notification->created_at}\n";
        echo "  Read: " . ($notification->read_at ? 'YES' : 'NO') . "\n";
        echo str_repeat("-", 30) . "\n";
    }
} else {
    echo "❌ NO approval/rejection notifications found!\n";
    
    echo "\nThis suggests the real approval process is NOT triggering notifications.\n";
    
    // Let's check what's happening in the real approval process
    echo "\nLet's investigate the real approval process...\n";
    
    // Check if there are any posts that were approved by Jayvee
    $jayveeApprovals = App\Models\Post::where('approved_by', 2) // Jayvee's ID
        ->where('group_id', 3) // PSITS
        ->where('created_at', '>=', now()->subHours(2))
        ->get();
    
    echo "Posts approved by Jayvee in last 2 hours: {$jayveeApprovals->count()}\n";
    foreach ($jayveeApprovals as $post) {
        echo "- Post ID: {$post->id}, Author: {$post->user->name}, Title: \"{$post->title}\"\n";
        echo "  Approved at: {$post->approved_at}\n";
        
        // Check if notification should have been sent
        if ($post->user_id !== 2) { // Not self-approval
            echo "  Should notify: YES (different user)\n";
            
            // Check if user wants notifications
            $wantsNotifications = $post->user->wantsNotification('group_post_approvals');
            echo "  User wants notifications: " . ($wantsNotifications ? 'YES' : 'NO') . "\n";
            
            // Check if notification exists for this post
            $postNotification = $post->user->notifications()
                ->where('type', 'App\Notifications\GroupPostApproved')
                ->whereJsonContains('data->post_id', $post->id)
                ->first();
            
            echo "  Notification exists: " . ($postNotification ? 'YES' : 'NO') . "\n";
            
        } else {
            echo "  Should notify: NO (self-approval)\n";
        }
        echo str_repeat("-", 30) . "\n";
    }
}

// Check all notifications for Arjohn to see what types he's receiving
echo "\nAll recent notifications for {$arjohn->name} (last 2 hours):\n";
$allRecentNotifications = $arjohn->notifications()
    ->where('created_at', '>=', now()->subHours(2))
    ->orderBy('created_at', 'desc')
    ->get();

echo "Total recent notifications: {$allRecentNotifications->count()}\n";
foreach ($allRecentNotifications as $notification) {
    echo "- " . class_basename($notification->type) . ": {$notification->data['message']}\n";
    echo "  Created: {$notification->created_at}\n";
}

echo "\n🏁 Investigation completed!\n";
