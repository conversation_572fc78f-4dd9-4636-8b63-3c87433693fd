<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Simple Duplicate Notification Test\n";
echo "=====================================\n\n";

// Get test users
$user1 = App\Models\User::find(2); // Post owner
$user2 = App\Models\User::find(3); // Reactor

echo "Test Users:\n";
echo "User 1 (Post Owner): {$user1->name} (ID: {$user1->id})\n";
echo "User 2 (Reactor): {$user2->name} (ID: {$user2->id})\n\n";

// Create a simple test post
$post = App\Models\Post::create([
    'title' => 'Simple Duplicate Test',
    'content' => 'Testing for duplicates.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Count notifications before
$beforeCount = $user1->notifications()->count();
echo "Notifications before: {$beforeCount}\n";

// Create reaction manually (without using the controller)
$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

echo "Created reaction: {$reaction->type} by {$user2->name}\n";

// Fire the event manually
echo "Firing PostReactionAdded event...\n";
event(new App\Events\PostReactionAdded($user2, $post, $reaction));

// Wait a moment
echo "Waiting 2 seconds...\n";
sleep(2);

// Count notifications after
$afterCount = $user1->notifications()->count();
echo "Notifications after: {$afterCount}\n";

$increase = $afterCount - $beforeCount;
echo "Increase: {$increase}\n";

if ($increase === 1) {
    echo "✅ SUCCESS: Exactly 1 notification created (no duplicates)\n";
    
    // Show the notification
    $latest = $user1->notifications()->latest()->first();
    if ($latest) {
        echo "Latest notification: {$latest->data['message']}\n";
        echo "Type: {$latest->data['type']}\n";
    }
} elseif ($increase > 1) {
    echo "❌ FAILED: {$increase} notifications created - DUPLICATES DETECTED!\n";
    
    // Show recent notifications
    $recent = $user1->notifications()->latest()->limit($increase)->get();
    foreach ($recent as $notification) {
        echo "- {$notification->data['type']}: {$notification->data['message']}\n";
    }
} else {
    echo "❌ FAILED: No notification created\n";
}

// Clean up
echo "\nCleaning up...\n";
$reaction->delete();
$post->delete();
echo "✅ Cleaned up\n";

echo "\n🏁 Simple test completed!\n";
