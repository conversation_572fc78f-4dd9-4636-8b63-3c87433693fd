<?php

namespace App\Events;

use App\Models\User;
use App\Models\Post;
use App\Models\Share;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostSharedEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $post;
    public $share;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Post $post, Share $share)
    {
        $this->user = $user;
        $this->post = $post;
        $this->share = $share;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.User.' . $this->post->user_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => uniqid(),
            'type' => 'post_shared',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'share_id' => $this->share->id,
            'message' => "{$this->user->name} shared your post",
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ];
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        if ($this->post->group_id) {
            return route('groups.show', $this->post->group->slug) . '#post-' . $this->post->id;
        } elseif ($this->post->organization_id) {
            return route('organizations.show', $this->post->organization->slug) . '#post-' . $this->post->id;
        } else {
            return route('profile.user', $this->post->user) . '#post-' . $this->post->id;
        }
    }
}
