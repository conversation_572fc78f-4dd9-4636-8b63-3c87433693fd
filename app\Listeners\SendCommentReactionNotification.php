<?php

namespace App\Listeners;

use App\Events\CommentReactionAdded;
use App\Notifications\CommentReacted;
class SendCommentReactionNotification
{

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CommentReactionAdded $event): void
    {
        // Don't notify if user reacted to their own comment
        if ($event->user->id === $event->comment->user_id) {
            return;
        }

        // Check if the comment owner wants to receive this type of notification
        if (!$event->comment->user->wantsNotification('comment_reactions')) {
            return;
        }

        // Check for duplicate notifications
        $existingNotification = $event->comment->user->notifications()
            ->where('type', 'App\Notifications\CommentReacted')
            ->whereJsonContains('data->user_id', $event->user->id)
            ->whereJsonContains('data->comment_id', $event->comment->id)
            ->whereJsonContains('data->reaction_type', $event->reaction->type)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($existingNotification) {
            return; // Skip duplicate notification
        }

        // Send notification to the comment owner
        $event->comment->user->notify(new CommentReacted($event->user, $event->comment, $event->reaction));
    }
}
