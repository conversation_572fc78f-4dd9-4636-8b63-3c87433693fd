<?php

namespace App\Listeners;

use App\Events\CommentReactionAdded;
use App\Notifications\CommentReacted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendCommentReactionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CommentReactionAdded $event): void
    {
        // Don't notify if user reacted to their own comment
        if ($event->user->id === $event->comment->user_id) {
            return;
        }

        // Check if the comment owner wants to receive this type of notification
        if (!$event->comment->user->wantsNotification('comment_reactions')) {
            return;
        }

        // Send notification to the comment owner
        $event->comment->user->notify(new CommentReacted($event->user, $event->comment, $event->reaction));
    }
}
