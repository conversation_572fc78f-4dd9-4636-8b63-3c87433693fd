<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Direct Notifications\n";
echo "=================================\n\n";

$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

echo "Test users:\n";
echo "- Arjohn: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Jayvee: {$jayvee->name} (ID: {$jayvee->id})\n\n";

// Create a test post for the notification
$testPost = App\Models\Post::create([
    'title' => 'Direct Notification Test - ' . now()->format('H:i:s'),
    'content' => 'Testing direct notification creation.',
    'user_id' => $arjohn->id,
    'group_id' => $psits->id,
    'approval_status' => 'approved',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: \"{$testPost->title}\" (ID: {$testPost->id})\n\n";

// Count notifications before
$notificationsBefore = $arjohn->notifications()->count();
echo "Arjohn's notifications before: {$notificationsBefore}\n";

// Test 1: Direct GroupPostApproved notification
echo "\n=== TEST 1: Direct GroupPostApproved Notification ===\n";

try {
    $notification = new \App\Notifications\GroupPostApproved($testPost, $psits, $jayvee);
    
    echo "Notification created successfully\n";
    
    // Check notification channels
    $channels = $notification->via($arjohn);
    echo "Notification channels: " . implode(', ', $channels) . "\n";
    
    // Check if user wants this notification type
    $wantsNotification = $arjohn->wantsNotification('group_post_approvals');
    echo "User wants group_post_approvals: " . ($wantsNotification ? 'YES' : 'NO') . "\n";
    
    // Check user's notifications_enabled setting
    echo "User notifications_enabled: " . ($arjohn->notifications_enabled ? 'YES' : 'NO') . "\n";
    
    if (in_array('database', $channels)) {
        echo "Sending notification to database...\n";
        
        // Send the notification
        $arjohn->notify($notification);
        echo "✅ Notification sent\n";
        
        // Wait a moment
        sleep(1);
        
        // Check if it was created
        $notificationsAfter = $arjohn->notifications()->count();
        echo "Arjohn's notifications after: {$notificationsAfter}\n";
        
        $increase = $notificationsAfter - $notificationsBefore;
        echo "Increase: {$increase}\n";
        
        if ($increase > 0) {
            echo "✅ SUCCESS: Direct notification created!\n";
            
            // Get the notification details
            $latestNotification = $arjohn->notifications()->latest()->first();
            echo "Latest notification type: {$latestNotification->type}\n";
            echo "Latest notification message: \"{$latestNotification->data['message']}\"\n";
            echo "Latest notification created: {$latestNotification->created_at}\n";
        } else {
            echo "❌ FAILED: Direct notification not created\n";
            
            // Debug the notification system
            echo "\nDEBUGGING:\n";
            
            // Check if notifications table exists and is accessible
            try {
                $notificationsTableCount = \Illuminate\Support\Facades\DB::table('notifications')->count();
                echo "- Notifications table accessible: YES (total: {$notificationsTableCount})\n";
            } catch (Exception $e) {
                echo "- Notifications table accessible: NO - " . $e->getMessage() . "\n";
            }
            
            // Check if the notification was created but not associated with the user
            try {
                $recentNotifications = \Illuminate\Support\Facades\DB::table('notifications')
                    ->where('created_at', '>=', now()->subMinutes(1))
                    ->get();
                
                echo "- Recent notifications in table: {$recentNotifications->count()}\n";
                foreach ($recentNotifications as $notif) {
                    echo "  * ID: {$notif->id}, Type: {$notif->type}, Notifiable: {$notif->notifiable_id}\n";
                }
            } catch (Exception $e) {
                echo "- Error checking recent notifications: " . $e->getMessage() . "\n";
            }
            
            // Check if there's an issue with the User model's notifications relationship
            try {
                $userNotificationsQuery = $arjohn->notifications()->toSql();
                echo "- User notifications query: {$userNotificationsQuery}\n";
            } catch (Exception $e) {
                echo "- Error with user notifications query: " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "❌ Database channel not enabled\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error creating notification: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

// Test 2: Simple test notification
echo "\n=== TEST 2: Simple Test Notification ===\n";

try {
    // Create a very simple notification
    $arjohn->notify(new \App\Notifications\PostReacted($jayvee, $testPost, (object)['type' => 'like']));
    
    sleep(1);
    
    $notificationsAfter2 = $arjohn->notifications()->count();
    $increase2 = $notificationsAfter2 - $notificationsAfter;
    
    echo "Simple notification increase: {$increase2}\n";
    
    if ($increase2 > 0) {
        echo "✅ Simple notification works\n";
    } else {
        echo "❌ Simple notification also fails\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error with simple notification: " . $e->getMessage() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
$testPost->delete();
echo "✅ Test post deleted\n";

echo "\n🏁 Direct notification debug completed!\n";
