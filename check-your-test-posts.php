<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Checking Your Test Posts\n";
echo "===========================\n\n";

// Get all pending posts (your test posts)
$pendingPosts = App\Models\Post::where('approval_status', 'pending')
    ->with(['user', 'group'])
    ->orderBy('created_at', 'desc')
    ->get();

echo "Found {$pendingPosts->count()} pending posts:\n";
echo "----------------------------------------\n";

foreach ($pendingPosts as $post) {
    echo "Post ID: {$post->id}\n";
    echo "Title: \"{$post->title}\"\n";
    echo "Content: " . substr($post->content, 0, 100) . "...\n";
    echo "Author: {$post->user->name} (ID: {$post->user_id})\n";
    echo "Group: {$post->group->name} (ID: {$post->group_id})\n";
    echo "Status: {$post->approval_status}\n";
    echo "Created: {$post->created_at}\n";
    echo "Author notification preferences: " . json_encode($post->user->notification_preferences) . "\n";
    echo "Author wants group_post_approvals: " . ($post->user->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
    echo str_repeat("-", 50) . "\n";
}

if ($pendingPosts->count() === 0) {
    echo "No pending posts found. Let me check recently created posts...\n\n";
    
    $recentPosts = App\Models\Post::with(['user', 'group'])
        ->where('created_at', '>=', now()->subHours(2))
        ->orderBy('created_at', 'desc')
        ->get();
    
    echo "Recent posts (last 2 hours): {$recentPosts->count()}\n";
    foreach ($recentPosts as $post) {
        echo "- ID: {$post->id}, Title: \"{$post->title}\", Status: {$post->approval_status}, Author: {$post->user->name}\n";
    }
}

echo "\n🏁 Check completed!\n";
