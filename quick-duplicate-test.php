<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Quick Duplicate Test\n";
echo "======================\n\n";

// Get test users
$user1 = App\Models\User::find(2);
$user2 = App\Models\User::find(3);

echo "Users: {$user1->name} and {$user2->name}\n";

// Create test post
$post = App\Models\Post::create([
    'title' => 'Quick Test',
    'content' => 'Testing.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created post: {$post->id}\n";

// Create reaction
$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

echo "Created reaction: {$reaction->id}\n";

// Count before
$before = $user1->notifications()->count();
echo "Before: {$before}\n";

// Fire event
event(new App\Events\PostReactionAdded($user2, $post, $reaction));
sleep(1);

// Count after
$after = $user1->notifications()->count();
echo "After: {$after}\n";
echo "Increase: " . ($after - $before) . "\n";

// Clean up
$reaction->delete();
$post->delete();

echo "Done!\n";
