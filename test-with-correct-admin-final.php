<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Final Test with Correct Group Admin\n";
echo "======================================\n\n";

// Get users
$arjohn = App\Models\User::find(7); // Ar<PERSON>hn Egamen (post author)
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova (PSITS admin)
$psits = App\Models\Group::find(3); // PSITS group

echo "Test Setup:\n";
echo "- Post Author: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Group Admin: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n\n";

// Verify admin permissions
echo "Admin Verification:\n";
echo "- Can {$jayvee->name} moderate {$psits->name}? " . ($psits->userCanModerate($jayvee) ? 'YES' : 'NO') . "\n";

$membership = \Illuminate\Support\Facades\DB::table('group_members')
    ->where('group_id', $psits->id)
    ->where('user_id', $jayvee->id)
    ->first();

echo "- {$jayvee->name}'s role in {$psits->name}: {$membership->role}\n";
echo "- {$jayvee->name}'s status in {$psits->name}: {$membership->status}\n\n";

// Create a test post that needs approval
echo "Creating test post for approval...\n";
$testPost = App\Models\Post::create([
    'title' => 'Final Test Post - ' . now()->format('H:i:s'),
    'content' => 'This post will be approved by the correct group admin (Jayvee).',
    'user_id' => $arjohn->id,
    'group_id' => $psits->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "✅ Created test post: \"{$testPost->title}\" (ID: {$testPost->id})\n";
echo "Post status: {$testPost->approval_status}\n\n";

// Count notifications before
$notificationsBefore = $arjohn->notifications()->count();
echo "Arjohn's notifications before approval: {$notificationsBefore}\n\n";

// Test approval with correct group admin
echo "=== APPROVAL TEST ===\n";
echo "Authenticating as {$jayvee->name} (correct group admin)...\n";
auth()->login($jayvee);

try {
    $controller = new App\Http\Controllers\GroupController();
    echo "Calling approvePost method...\n";
    
    $response = $controller->approvePost($psits, $testPost);
    echo "✅ Approval method executed successfully\n";
    
    // Check post status
    $testPost->refresh();
    echo "Post status after approval: {$testPost->approval_status}\n";
    echo "Approved at: {$testPost->approved_at}\n";
    echo "Approved by: {$testPost->approved_by} (should be {$jayvee->id})\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications
    $notificationsAfter = $arjohn->notifications()->count();
    echo "Arjohn's notifications after approval: {$notificationsAfter}\n";
    
    $increase = $notificationsAfter - $notificationsBefore;
    echo "Notification increase: {$increase}\n";
    
    if ($increase > 0) {
        echo "✅ SUCCESS: Group post approval notification created!\n";
        
        // Get the notification details
        $approvalNotification = $arjohn->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
        
        if ($approvalNotification) {
            echo "\nNotification Details:\n";
            echo "- Message: \"{$approvalNotification->data['message']}\"\n";
            echo "- Approved by: {$approvalNotification->data['user_name']}\n";
            echo "- Group: {$approvalNotification->data['group_name']}\n";
            echo "- Post title: {$approvalNotification->data['post_title']}\n";
            echo "- Created: {$approvalNotification->created_at}\n";
            echo "- Read: " . ($approvalNotification->read_at ? 'YES' : 'NO') . "\n";
        }
    } else {
        echo "❌ FAILED: No notification was created\n";
        
        // Debug information
        echo "\nDEBUG INFO:\n";
        echo "- Post author ID: {$testPost->user_id}\n";
        echo "- Group admin ID: {$jayvee->id}\n";
        echo "- Same user check: " . ($testPost->user_id === $jayvee->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
        echo "- Author wants group_post_approvals: " . ($arjohn->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
        echo "- Author notifications enabled: " . ($arjohn->notifications_enabled ? 'YES' : 'NO') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during approval: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

// Logout
auth()->logout();

// Clean up
echo "\nCleaning up test post...\n";
$testPost->delete();
echo "✅ Test post deleted\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 FINAL RESULT:\n";
echo "The group post approval notification system is ";
echo ($increase > 0 ? "WORKING CORRECTLY!" : "NOT WORKING - needs investigation");
echo "\n" . str_repeat("=", 50) . "\n";

echo "\n🏁 Final test completed!\n";
