<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Share Notifications...\n\n";

// Get test data
$sharer = App\Models\User::find(1); // Admin
$postOwner = App\Models\User::find(3); // Juan <PERSON>
$post = App\Models\Post::where('user_id', $postOwner->id)->first();

if (!$sharer || !$postOwner || !$post) {
    echo "❌ Missing test data\n";
    exit;
}

echo "Test Data:\n";
echo "Sharer: {$sharer->name} (ID: {$sharer->id})\n";
echo "Post Owner: {$postOwner->name} (ID: {$postOwner->id})\n";
echo "Post: {$post->title} (ID: {$post->id})\n\n";

// Test notification preferences
echo "Testing notification preferences:\n";
echo "Post owner wants post_shares: " . ($postOwner->wantsNotification('post_shares') ? 'YES' : 'NO') . "\n\n";

// Create a share
$share = App\Models\Share::create([
    'user_id' => $sharer->id,
    'post_id' => $post->id,
    'content' => 'Test share',
    'privacy_scope' => 'public'
]);

echo "✅ Share created (ID: {$share->id})\n";

// Fire the event
echo "🚀 Firing PostSharedEvent...\n";
event(new App\Events\PostSharedEvent($sharer, $post, $share));

echo "✅ Event fired successfully!\n\n";

// Wait a moment for processing
sleep(2);

// Check if notification was created
$latestNotification = $postOwner->notifications()
    ->where('type', 'App\Notifications\PostShared')
    ->latest()
    ->first();

if ($latestNotification) {
    echo "✅ Share notification found!\n";
    echo "Created: {$latestNotification->created_at}\n";
    echo "Type: {$latestNotification->data['type']}\n";
    echo "Message: {$latestNotification->data['message']}\n";
} else {
    echo "❌ No share notification found\n";
    
    // Check if there are any pending jobs
    $pendingJobs = \Illuminate\Support\Facades\DB::table('jobs')->count();
    echo "Pending jobs: $pendingJobs\n";
}

echo "\n🎯 Test Complete!\n";
