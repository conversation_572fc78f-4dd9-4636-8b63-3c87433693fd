<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "📋 Checking Pending Posts and Recent Notifications\n";
echo "==================================================\n\n";

// Check all groups and their pending posts
$groups = App\Models\Group::all();

echo "Groups and their pending posts:\n";
echo "-------------------------------\n";

foreach ($groups as $group) {
    $pendingCount = App\Models\Post::where('group_id', $group->id)
        ->where('approval_status', 'pending')
        ->count();
    
    echo "Group: {$group->name} (ID: {$group->id})\n";
    echo "- Post approval setting: {$group->post_approval}\n";
    echo "- Pending posts: {$pendingCount}\n";
    
    if ($pendingCount > 0) {
        $pendingPosts = App\Models\Post::where('group_id', $group->id)
            ->where('approval_status', 'pending')
            ->with('user')
            ->get();
        
        foreach ($pendingPosts as $post) {
            echo "  * Post ID: {$post->id}, Title: \"{$post->title}\", Author: {$post->user->name}\n";
        }
    }
    echo "\n";
}

// Check recent group post approval notifications
echo "Recent Group Post Approval Notifications:\n";
echo "-----------------------------------------\n";

$recentApprovalNotifications = \Illuminate\Support\Facades\DB::table('notifications')
    ->where('type', 'App\Notifications\GroupPostApproved')
    ->where('created_at', '>=', now()->subDays(7))
    ->orderBy('created_at', 'desc')
    ->get();

if ($recentApprovalNotifications->count() > 0) {
    echo "Found {$recentApprovalNotifications->count()} recent approval notifications:\n";
    foreach ($recentApprovalNotifications as $notification) {
        $data = json_decode($notification->data, true);
        $user = App\Models\User::find($notification->notifiable_id);
        echo "- To: {$user->name}, Message: \"{$data['message']}\", Created: {$notification->created_at}\n";
    }
} else {
    echo "No recent group post approval notifications found.\n";
}

echo "\n";

// Check recent group post rejection notifications
$recentRejectionNotifications = \Illuminate\Support\Facades\DB::table('notifications')
    ->where('type', 'App\Notifications\GroupPostRejected')
    ->where('created_at', '>=', now()->subDays(7))
    ->orderBy('created_at', 'desc')
    ->get();

if ($recentRejectionNotifications->count() > 0) {
    echo "Found {$recentRejectionNotifications->count()} recent rejection notifications:\n";
    foreach ($recentRejectionNotifications as $notification) {
        $data = json_decode($notification->data, true);
        $user = App\Models\User::find($notification->notifiable_id);
        echo "- To: {$user->name}, Message: \"{$data['message']}\", Created: {$notification->created_at}\n";
    }
} else {
    echo "No recent group post rejection notifications found.\n";
}

// Check if there are any approved posts in the last 7 days
echo "\nRecently Approved Posts:\n";
echo "------------------------\n";

$recentlyApproved = App\Models\Post::where('approval_status', 'approved')
    ->where('approved_at', '>=', now()->subDays(7))
    ->with(['user', 'group'])
    ->orderBy('approved_at', 'desc')
    ->get();

if ($recentlyApproved->count() > 0) {
    echo "Found {$recentlyApproved->count()} recently approved posts:\n";
    foreach ($recentlyApproved as $post) {
        $approver = App\Models\User::find($post->approved_by);
        echo "- Post: \"{$post->title}\" by {$post->user->name} in {$post->group->name}\n";
        echo "  Approved by: {$approver->name} at {$post->approved_at}\n";
    }
} else {
    echo "No recently approved posts found.\n";
}

echo "\n🏁 Check completed!\n";
