<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

$user = App\Models\User::find(3);
$notification = $user->notifications()
    ->where('type', 'App\Notifications\GroupPostApproved')
    ->latest()
    ->first();

if ($notification) {
    echo "✅ Notification found!\n";
    echo "Created: {$notification->created_at}\n";
    echo "Type: {$notification->data['type']}\n";
    echo "Message: {$notification->data['message']}\n";
} else {
    echo "❌ No GroupPostApproved notification found\n";
}

// Check total notifications for this user
$totalNotifications = $user->notifications()->count();
echo "Total notifications for user: $totalNotifications\n";

// Check recent notifications
echo "\nRecent notifications:\n";
$recent = $user->notifications()->latest()->limit(3)->get();
foreach ($recent as $n) {
    echo "- {$n->data['type']} at {$n->created_at}\n";
}
