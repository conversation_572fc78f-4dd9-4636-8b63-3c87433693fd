<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Post Approval Notifications...\n\n";

// Get test data
$user = App\Models\User::find(3); // Juan <PERSON> Cruz
$group = App\Models\Group::first();
$post = App\Models\Post::where('group_id', $group->id ?? 0)->first();

if (!$user || !$group || !$post) {
    echo "❌ Missing test data\n";
    echo "User: " . ($user ? $user->name : 'NOT FOUND') . "\n";
    echo "Group: " . ($group ? $group->name : 'NOT FOUND') . "\n";
    echo "Post: " . ($post ? $post->title : 'NOT FOUND') . "\n";
    exit;
}

echo "Test Data:\n";
echo "User: {$user->name} (ID: {$user->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n";
echo "Post: {$post->title} (ID: {$post->id})\n\n";

// Test notification preferences
echo "Testing notification preferences:\n";
echo "User wants group_post_approvals: " . ($user->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";

// Get user's notification preferences
$preferences = $user->notification_preferences ?? [];
echo "Raw preferences: " . json_encode($preferences) . "\n\n";

// Test creating the notification manually
echo "Creating GroupPostApproved notification manually...\n";
$approver = App\Models\User::find(1); // Admin user

try {
    $notification = new App\Notifications\GroupPostApproved($post, $group, $approver);
    
    // Check what channels it will use
    $channels = $notification->via($user);
    echo "Notification channels: " . implode(', ', $channels) . "\n";
    
    if (empty($channels)) {
        echo "❌ No channels - notification will not be sent!\n";
    } else {
        // Send the notification
        $user->notify($notification);
        echo "✅ Notification sent successfully!\n";
        
        // Check if it was created in database
        $latestNotification = $user->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
            
        if ($latestNotification) {
            echo "✅ Notification found in database\n";
            echo "Created at: {$latestNotification->created_at}\n";
            echo "Data: " . json_encode($latestNotification->data) . "\n";
        } else {
            echo "❌ Notification not found in database\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error creating notification: " . $e->getMessage() . "\n";
}

echo "\n🎯 Test Complete!\n";
