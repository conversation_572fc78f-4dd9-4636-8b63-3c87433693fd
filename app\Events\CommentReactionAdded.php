<?php

namespace App\Events;

use App\Models\User;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommentReactionAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $comment;
    public $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Comment $comment, Reaction $reaction)
    {
        $this->user = $user;
        $this->comment = $comment;
        $this->reaction = $reaction;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.User.' . $this->comment->user_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => uniqid(),
            'type' => 'comment_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'post_id' => $this->comment->commentable_id,
            'post_title' => $this->comment->commentable->title ?? 'a post',
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getCommentUrl(),
            'created_at' => now()->toISOString(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        $reactionLabel = $this->getReactionLabel($this->reaction->type);
        return "{$this->user->name} reacted {$reactionLabel} to your comment";
    }

    /**
     * Get the comment URL
     */
    private function getCommentUrl(): string
    {
        $post = $this->comment->commentable;
        
        if ($post->group_id) {
            return route('groups.show', $post->group->slug) . '#comment-' . $this->comment->id;
        } elseif ($post->organization_id) {
            return route('organizations.show', $post->organization->slug) . '#comment-' . $this->comment->id;
        } else {
            return route('profile.user', $post->user) . '#comment-' . $this->comment->id;
        }
    }

    /**
     * Get reaction emoji
     */
    private function getReactionEmoji(string $type): string
    {
        $emojis = [
            'like' => '👍',
            'love' => '❤️',
            'haha' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];

        return $emojis[$type] ?? '👍';
    }

    /**
     * Get reaction label
     */
    private function getReactionLabel(string $type): string
    {
        $labels = [
            'like' => 'with a like',
            'love' => 'with love',
            'haha' => 'with laughter',
            'wow' => 'with wow',
            'sad' => 'with sadness',
            'angry' => 'with anger',
        ];

        return $labels[$type] ?? 'with a like';
    }
}
