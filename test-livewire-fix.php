<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Livewire PostApproval Fix\n";
echo "====================================\n\n";

$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

echo "Test Setup:\n";
echo "- Author: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Admin: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n\n";

// Create a test post
$testPost = App\Models\Post::create([
    'title' => 'Livewire Fix Test - ' . now()->format('H:i:s'),
    'content' => 'Testing the fixed Livewire component.',
    'user_id' => $arjohn->id,
    'group_id' => $psits->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: \"{$testPost->title}\" (ID: {$testPost->id})\n";
echo "Post status: {$testPost->approval_status}\n\n";

// Count notifications before
$notificationsBefore = $arjohn->notifications()->count();
echo "Arjohn's notifications before: {$notificationsBefore}\n\n";

// Test the Livewire component
echo "=== TESTING LIVEWIRE COMPONENT ===\n";

// Authenticate as Jayvee
auth()->login($jayvee);
echo "✅ Authenticated as {$jayvee->name}\n";

try {
    // Create the Livewire component
    $component = new App\Livewire\PostApproval();
    $component->mount($testPost, $psits);
    
    echo "✅ Livewire component created\n";
    
    // Call the approvePost method
    $component->approvePost();
    
    echo "✅ Livewire approvePost() method executed\n";
    
    // Check post status
    $testPost->refresh();
    echo "Post status after: {$testPost->approval_status}\n";
    echo "Approved at: {$testPost->approved_at}\n";
    echo "Approved by: {$testPost->approved_by}\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications
    $notificationsAfter = $arjohn->notifications()->count();
    echo "Arjohn's notifications after: {$notificationsAfter}\n";
    
    $increase = $notificationsAfter - $notificationsBefore;
    echo "Notification increase: {$increase}\n";
    
    if ($increase > 0) {
        echo "✅ SUCCESS: Livewire component now sends notifications!\n";
        
        // Get the notification details
        $approvalNotification = $arjohn->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
        
        if ($approvalNotification) {
            echo "\nNotification Details:\n";
            echo "- Message: \"{$approvalNotification->data['message']}\"\n";
            echo "- Approved by: {$approvalNotification->data['user_name']}\n";
            echo "- Group: {$approvalNotification->data['group_name']}\"\n";
            echo "- Post title: {$approvalNotification->data['post_title']}\n";
            echo "- Created: {$approvalNotification->created_at}\n";
        }
    } else {
        echo "❌ FAILED: Livewire component still not sending notifications\n";
        
        // Debug
        echo "\nDEBUG INFO:\n";
        echo "- Post user ID: {$testPost->user_id}\n";
        echo "- Approver ID: {$jayvee->id}\n";
        echo "- Same user: " . ($testPost->user_id === $jayvee->id ? 'YES' : 'NO') . "\n";
        echo "- User wants notifications: " . ($arjohn->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing Livewire component: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

auth()->logout();

// Test rejection as well
echo "\n=== TESTING REJECTION ===\n";

// Create another test post for rejection
$testPost2 = App\Models\Post::create([
    'title' => 'Livewire Rejection Test - ' . now()->format('H:i:s'),
    'content' => 'Testing the fixed Livewire rejection.',
    'user_id' => $arjohn->id,
    'group_id' => $psits->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post for rejection: \"{$testPost2->title}\" (ID: {$testPost2->id})\n";

$notificationsBefore2 = $arjohn->notifications()->count();
echo "Notifications before rejection: {$notificationsBefore2}\n";

auth()->login($jayvee);

try {
    $component2 = new App\Livewire\PostApproval();
    $component2->mount($testPost2, $psits);
    
    // Call the rejectPost method
    $component2->rejectPost();
    
    echo "✅ Livewire rejectPost() method executed\n";
    
    $testPost2->refresh();
    echo "Post status after rejection: {$testPost2->approval_status}\n";
    
    sleep(2);
    
    $notificationsAfter2 = $arjohn->notifications()->count();
    $increase2 = $notificationsAfter2 - $notificationsBefore2;
    
    echo "Notification increase for rejection: {$increase2}\n";
    
    if ($increase2 > 0) {
        echo "✅ SUCCESS: Rejection notifications also work!\n";
    } else {
        echo "❌ FAILED: Rejection notifications not working\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing rejection: " . $e->getMessage() . "\n";
}

auth()->logout();

// Clean up
echo "\nCleaning up...\n";
$testPost->delete();
$testPost2->delete();
echo "✅ Test posts deleted\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 LIVEWIRE FIX RESULT:\n";
echo "The web interface approval notifications are now ";
echo (isset($increase) && $increase > 0 ? "WORKING!" : "STILL BROKEN");
echo "\n" . str_repeat("=", 50) . "\n";

echo "\n🏁 Livewire fix test completed!\n";
