<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing with CORRECT Group Admin\n";
echo "===================================\n\n";

// Get the correct users
$postAuthor = App\Models\User::find(7); // A<PERSON><PERSON>hn Egamen (post creator)
$groupAdmin = App\Models\User::find(2); // Jayvee Tyrone Cordova (PSITS group admin)
$psitsGroup = App\Models\Group::find(3); // PSITS group

echo "Correct Test Setup:\n";
echo "- Post Author: {$postAuthor->name} (ID: {$postAuthor->id})\n";
echo "- Group Admin: {$groupAdmin->name} (ID: {$groupAdmin->id})\n";
echo "- Group: {$psitsGroup->name} (ID: {$psitsGroup->id})\n\n";

// Verify group admin permissions
echo "Verifying Group Admin Permissions:\n";
echo "- Can {$groupAdmin->name} moderate {$psitsGroup->name}? " . ($psitsGroup->userCanModerate($groupAdmin) ? 'YES' : 'NO') . "\n";

// Check if Jayvee is actually the group admin
$groupMembership = \Illuminate\Support\Facades\DB::table('group_members')
    ->where('group_id', $psitsGroup->id)
    ->where('user_id', $groupAdmin->id)
    ->first();

if ($groupMembership) {
    echo "- {$groupAdmin->name}'s role in {$psitsGroup->name}: {$groupMembership->role}\n";
} else {
    echo "- {$groupAdmin->name} is not a member of {$psitsGroup->name}\n";
}

// Check the wrong admin too
$wrongAdmin = App\Models\User::find(1); // Admin user
echo "- Can {$wrongAdmin->name} moderate {$psitsGroup->name}? " . ($psitsGroup->userCanModerate($wrongAdmin) ? 'YES' : 'NO') . "\n\n";

// Get Arjohn's pending posts in PSITS
$pendingPosts = App\Models\Post::where('group_id', $psitsGroup->id)
    ->where('user_id', $postAuthor->id)
    ->where('approval_status', 'pending')
    ->get();

echo "Arjohn's pending posts in PSITS: {$pendingPosts->count()}\n";

if ($pendingPosts->count() === 0) {
    echo "No pending posts found. Creating new test posts...\n";
    
    // Create test posts
    $testPost1 = App\Models\Post::create([
        'title' => 'Test Approval with Correct Admin - ' . now()->format('H:i:s'),
        'content' => 'This post should be approved by Jayvee (correct group admin).',
        'user_id' => $postAuthor->id,
        'group_id' => $psitsGroup->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'published_at' => now(),
    ]);
    
    $testPost2 = App\Models\Post::create([
        'title' => 'Test Rejection with Correct Admin - ' . now()->format('H:i:s'),
        'content' => 'This post should be rejected by Jayvee (correct group admin).',
        'user_id' => $postAuthor->id,
        'group_id' => $psitsGroup->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'published_at' => now(),
    ]);
    
    $pendingPosts = collect([$testPost1, $testPost2]);
    echo "✅ Created 2 new test posts\n";
}

echo "\nPending posts to test:\n";
foreach ($pendingPosts as $post) {
    echo "- Post ID: {$post->id}, Title: \"{$post->title}\"\n";
}

// Count notifications before
$notificationsBefore = $postAuthor->notifications()->count();
echo "\nArjohn's notifications before: {$notificationsBefore}\n\n";

// Test with CORRECT group admin
echo "=== Testing with CORRECT Group Admin (Jayvee) ===\n";

// Authenticate as the correct group admin
auth()->login($groupAdmin);
echo "✅ Authenticated as {$groupAdmin->name} (correct group admin)\n";

// Test approval of first post
$firstPost = $pendingPosts->first();
echo "\nApproving post: \"{$firstPost->title}\" (ID: {$firstPost->id})\n";

try {
    $controller = new App\Http\Controllers\GroupController();
    $response = $controller->approvePost($psitsGroup, $firstPost);
    
    echo "✅ Approval method executed successfully\n";
    
    // Check post status
    $firstPost->refresh();
    echo "Post status: {$firstPost->approval_status}\n";
    echo "Approved by: {$firstPost->approved_by} ({$groupAdmin->name})\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications
    $notificationsAfter = $postAuthor->notifications()->count();
    echo "Arjohn's notifications after: {$notificationsAfter}\n";
    
    $increase = $notificationsAfter - $notificationsBefore;
    echo "Notification increase: {$increase}\n";
    
    if ($increase > 0) {
        echo "✅ SUCCESS: Approval notification created by correct group admin!\n";
        
        // Get the notification
        $approvalNotification = $postAuthor->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
        
        if ($approvalNotification) {
            echo "Notification: \"{$approvalNotification->data['message']}\"\n";
            echo "Approved by: {$approvalNotification->data['user_name']}\n";
        }
    } else {
        echo "❌ FAILED: No notification created\n";
        
        // Debug
        echo "\nDEBUG INFO:\n";
        echo "- Post author ID: {$firstPost->user_id}\n";
        echo "- Group admin ID: {$groupAdmin->id}\n";
        echo "- Same user check: " . ($firstPost->user_id === $groupAdmin->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
        echo "- Author wants notifications: " . ($postAuthor->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

// Test rejection with second post if available
if ($pendingPosts->count() > 1) {
    echo "\n" . str_repeat("-", 50) . "\n";
    
    $secondPost = $pendingPosts->skip(1)->first();
    echo "Rejecting post: \"{$secondPost->title}\" (ID: {$secondPost->id})\n";
    
    $notificationsBefore2 = $postAuthor->notifications()->count();
    
    try {
        $response2 = $controller->rejectPost($psitsGroup, $secondPost);
        
        echo "✅ Rejection method executed successfully\n";
        
        sleep(2);
        
        $notificationsAfter2 = $postAuthor->notifications()->count();
        $increase2 = $notificationsAfter2 - $notificationsBefore2;
        
        echo "Notification increase for rejection: {$increase2}\n";
        
        if ($increase2 > 0) {
            echo "✅ SUCCESS: Rejection notification created by correct group admin!\n";
            
            $rejectionNotification = $postAuthor->notifications()
                ->where('type', 'App\Notifications\GroupPostRejected')
                ->latest()
                ->first();
            
            if ($rejectionNotification) {
                echo "Notification: \"{$rejectionNotification->data['message']}\"\n";
                echo "Rejected by: {$rejectionNotification->data['user_name']}\n";
            }
        } else {
            echo "❌ FAILED: No rejection notification created\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
}

// Logout
auth()->logout();

echo "\n🏁 Test with correct group admin completed!\n";
