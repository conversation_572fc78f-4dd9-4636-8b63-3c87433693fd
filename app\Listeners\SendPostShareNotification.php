<?php

namespace App\Listeners;

use App\Events\PostSharedEvent;
use App\Notifications\PostShared;
class SendPostShareNotification
{

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostSharedEvent $event): void
    {
        // Don't notify if user shared their own post
        if ($event->user->id === $event->post->user_id) {
            return;
        }

        // Check if the post owner wants to receive this type of notification
        if (!$event->post->user->wantsNotification('post_shares')) {
            return;
        }

        // Check for duplicate notifications
        $existingNotification = $event->post->user->notifications()
            ->where('type', 'App\Notifications\PostShared')
            ->whereJsonContains('data->user_id', $event->user->id)
            ->whereJsonContains('data->post_id', $event->post->id)
            ->whereJsonContains('data->share_id', $event->share->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($existingNotification) {
            return; // Skip duplicate notification
        }

        // Send notification to the post owner
        $event->post->user->notify(new PostShared($event->user, $event->post, $event->share));
    }
}
