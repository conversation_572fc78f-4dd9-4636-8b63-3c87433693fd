<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Syntax Fix\n";
echo "=====================\n\n";

try {
    // Try to compile the view
    $view = view('layouts.unilink-header');
    echo "✅ View compiles successfully!\n";
    
    // Try to render it (this will test the full syntax)
    $html = $view->render();
    echo "✅ View renders successfully!\n";
    echo "HTML length: " . strlen($html) . " characters\n";
    
} catch (Exception $e) {
    echo "❌ Syntax error still exists:\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n🏁 Syntax test completed!\n";
