<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Checking Real Pending Posts\n";
echo "==============================\n\n";

// Get users and group
$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

echo "Users:\n";
echo "- Arjohn: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Jayvee: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n";
echo "- Group post approval: {$psits->post_approval}\n\n";

// Check if there are any pending posts by <PERSON><PERSON><PERSON><PERSON> in PSITS
$pendingPosts = App\Models\Post::where('user_id', $arjohn->id)
    ->where('group_id', $psits->id)
    ->where('approval_status', 'pending')
    ->with(['user', 'group'])
    ->get();

echo "Pending posts by {$arjohn->name} in {$psits->name}: {$pendingPosts->count()}\n";

if ($pendingPosts->count() > 0) {
    echo "\nPending posts found:\n";
    foreach ($pendingPosts as $post) {
        echo "- Post ID: {$post->id}\n";
        echo "  Title: \"{$post->title}\"\n";
        echo "  Content: " . substr($post->content, 0, 50) . "...\n";
        echo "  Created: {$post->created_at}\n";
        echo "  Status: {$post->approval_status}\n";
        echo "  Published at: {$post->published_at}\n";
        echo str_repeat("-", 40) . "\n";
    }
    
    // Test approval on the first pending post
    $testPost = $pendingPosts->first();
    echo "\nTesting approval on: \"{$testPost->title}\" (ID: {$testPost->id})\n";
    
    // Count notifications before
    $notificationsBefore = $arjohn->notifications()->count();
    echo "Arjohn's notifications before: {$notificationsBefore}\n";
    
    // Authenticate as Jayvee and approve
    auth()->login($jayvee);
    echo "✅ Authenticated as {$jayvee->name}\n";
    
    try {
        $controller = new App\Http\Controllers\GroupController();
        $response = $controller->approvePost($psits, $testPost);
        
        echo "✅ Approval executed\n";
        
        // Check result
        $testPost->refresh();
        echo "Post status after: {$testPost->approval_status}\n";
        echo "Approved at: {$testPost->approved_at}\n";
        echo "Approved by: {$testPost->approved_by}\n";
        
        sleep(2);
        
        $notificationsAfter = $arjohn->notifications()->count();
        echo "Arjohn's notifications after: {$notificationsAfter}\n";
        
        $increase = $notificationsAfter - $notificationsBefore;
        echo "Notification increase: {$increase}\n";
        
        if ($increase > 0) {
            echo "✅ SUCCESS: Real pending post approval notification created!\n";
            
            $approvalNotification = $arjohn->notifications()
                ->where('type', 'App\Notifications\GroupPostApproved')
                ->latest()
                ->first();
            
            if ($approvalNotification) {
                echo "Notification: \"{$approvalNotification->data['message']}\"\n";
            }
        } else {
            echo "❌ FAILED: No notification created for real pending post\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    auth()->logout();
    
} else {
    echo "No pending posts found.\n";
    
    // Check all posts by Arjohn in PSITS
    $allPosts = App\Models\Post::where('user_id', $arjohn->id)
        ->where('group_id', $psits->id)
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();
    
    echo "\nRecent posts by {$arjohn->name} in {$psits->name}: {$allPosts->count()}\n";
    foreach ($allPosts as $post) {
        echo "- ID: {$post->id}, Title: \"{$post->title}\", Status: {$post->approval_status}, Created: {$post->created_at}\n";
    }
}

echo "\n🏁 Check completed!\n";
