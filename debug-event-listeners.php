<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Event Listeners\n";
echo "============================\n\n";

// Check registered event listeners
$eventDispatcher = app('events');

echo "Checking registered listeners for PostReactionAdded:\n";

// Get the listeners for PostReactionAdded
$listeners = $eventDispatcher->getListeners('App\Events\PostReactionAdded');

echo "Number of listeners: " . count($listeners) . "\n";

foreach ($listeners as $index => $listener) {
    echo "Listener {$index}: ";
    if (is_array($listener)) {
        echo get_class($listener[0]) . '@' . $listener[1] . "\n";
    } elseif (is_object($listener)) {
        echo get_class($listener) . "\n";
    } else {
        echo "Unknown listener type: " . gettype($listener) . "\n";
    }
}

echo "\n";

// Check other related events
echo "Checking other related events:\n";
$relatedEvents = [
    'App\Events\PostCommentAdded',
    'App\Events\CommentReactionAdded',
    'App\Events\PostSharedEvent'
];

foreach ($relatedEvents as $event) {
    $listeners = $eventDispatcher->getListeners($event);
    echo "Event: {$event} has " . count($listeners) . " listeners\n";
}

echo "\n";

// Test creating a notification directly without events
echo "Testing direct notification creation (without events):\n";

$user1 = App\Models\User::find(2);
$user2 = App\Models\User::find(3);

$post = App\Models\Post::create([
    'title' => 'Direct Notification Test',
    'content' => 'Testing direct notification.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

$beforeCount = $user1->notifications()->count();
echo "Notifications before direct notification: {$beforeCount}\n";

// Send notification directly
$user1->notify(new App\Notifications\PostReacted($user2, $post, $reaction));

$afterCount = $user1->notifications()->count();
echo "Notifications after direct notification: {$afterCount}\n";

$increase = $afterCount - $beforeCount;
echo "Increase: {$increase}\n";

if ($increase === 1) {
    echo "✅ Direct notification works correctly (no duplicates)\n";
} else {
    echo "❌ Direct notification also creates duplicates!\n";
}

// Clean up
$reaction->delete();
$post->delete();

echo "\n🏁 Debug completed!\n";
