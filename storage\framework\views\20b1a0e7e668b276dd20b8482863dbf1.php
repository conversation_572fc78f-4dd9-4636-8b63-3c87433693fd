<nav class="bg-white shadow-md border-b border-custom-second-darkest border-opacity-20 fixed w-full top-0 z-50">
    <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Left side - Logo and main nav -->
            <div class="flex items-center">
                <!-- Mobile menu button -->
                <button type="button" class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-custom-second-darkest hover:text-custom-darkest hover:bg-custom-lightest focus:outline-none focus:ring-2 focus:ring-inset focus:ring-custom-green" x-data x-on:click="$dispatch('toggle-sidebar')">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center ml-4 lg:ml-0">
                    <a href="<?php echo e(route('dashboard')); ?>" class="text-2xl font-bold">
                        <span class="text-custom-green">Uni</span><span class="text-custom-darkest">Link</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:ml-10 lg:flex lg:space-x-8">
                    <a href="<?php echo e(route('dashboard')); ?>" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('dashboard') ? 'text-custom-green bg-custom-lightest' : ''); ?>">
                        Home
                    </a>
                    <a href="<?php echo e(route('announcements')); ?>" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('announcements*') ? 'text-custom-green bg-custom-lightest' : ''); ?>">
                        Campus Announcements
                    </a>
                    <a href="<?php echo e(route('organizations.index')); ?>" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('organizations*') ? 'text-custom-green bg-custom-lightest' : ''); ?>">
                        My Organizations
                    </a>
                    <a href="<?php echo e(route('scholarships.index')); ?>" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium <?php echo e(request()->routeIs('scholarships*') ? 'text-custom-green bg-custom-lightest' : ''); ?>">
                        Find Scholarships
                    </a>
                </div>
            </div>

            <!-- Center - Search -->
            <div class="flex-1 max-w-2xl mx-4 lg:mx-8 hidden sm:block">
                <div class="relative">
                    <input type="text" placeholder="Search UniLink..." class="w-full pl-10 pr-4 py-2 bg-custom-lightest border-0 rounded-full focus:ring-2 focus:ring-custom-green focus:bg-white focus:shadow-md transition-all">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-custom-second-darkest" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Mobile Search Button -->
            <div class="sm:hidden">
                <button class="p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green rounded-full">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>

            <!-- Right side - notifications, profile -->
            <div class="flex items-center space-x-4">

                <!-- Notifications -->
                <div class="relative" x-data="notificationDropdown()" x-init="init()">
                    <button @click="toggleDropdown()" class="relative p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2 rounded-full">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                        </svg>
                        <!-- Notification badge -->
                        <span x-show="unreadCount > 0" x-text="unreadCount > 99 ? '99+' : unreadCount" class="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]"></span>
                    </button>

                    <!-- Notification dropdown -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <!-- Header -->
                        <div class="px-4 py-3 border-b border-custom-second-darkest border-opacity-20">
                            <div class="flex justify-between items-center">
                                <h3 class="text-sm font-medium text-custom-darkest">Notifications</h3>
                                <div class="flex items-center space-x-2">
                                    <!-- Browser notification permission button -->
                                    <button @click="requestNotificationPermission()"
                                            x-show="'Notification' in window && Notification.permission === 'default'"
                                            class="text-xs text-gray-500 hover:text-custom-green"
                                            title="Enable browser notifications">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                        </svg>
                                    </button>
                                    <!-- Mark all as read button -->
                                    <button @click="markAllAsRead()" x-show="unreadCount > 0" class="text-xs text-custom-green hover:text-custom-darkest">
                                        Mark all as read
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Browser notification permission prompt -->
                        <div x-show="showPermissionPrompt" x-transition class="px-4 py-3 bg-blue-50 border-b border-blue-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-blue-800 font-medium">Enable Browser Notifications</p>
                                    <p class="text-xs text-blue-600 mt-1">Get notified even when UniLink is not open</p>
                                    <div class="flex space-x-2 mt-2">
                                        <button @click="requestNotificationPermission()"
                                                class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                                            Enable
                                        </button>
                                        <button @click="dismissPermissionPrompt()"
                                                class="text-xs text-blue-600 hover:text-blue-800">
                                            Not now
                                        </button>
                                    </div>
                                </div>
                                <button @click="dismissPermissionPrompt()" class="flex-shrink-0 text-blue-400 hover:text-blue-600">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Notifications list -->
                        <div class="max-h-96 overflow-y-auto">
                            <template x-if="loading">
                                <div class="px-4 py-8 text-center">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-custom-green mx-auto"></div>
                                    <p class="text-sm text-custom-second-darkest mt-2">Loading notifications...</p>
                                </div>
                            </template>

                            <template x-if="!loading && notifications.length === 0">
                                <div class="px-4 py-8 text-center">
                                    <svg class="mx-auto h-12 w-12 text-custom-second-darkest opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                    </svg>
                                    <p class="text-sm text-custom-second-darkest mt-2">No notifications yet</p>
                                </div>
                            </template>

                            <template x-for="notification in notifications" :key="notification.id">
                                <div @click="markAsRead(notification.id, notification.data.url)"
                                     :class="notification.read_at ? 'bg-white' : 'bg-blue-50'"
                                     class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-start space-x-3">
                                        <!-- Avatar -->
                                        <img :src="notification.data.user_avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(notification.data.user_name || 'User') + '&color=7BC74D&background=EEEEEE&size=64'"
                                             :alt="notification.data.user_name"
                                             class="h-8 w-8 rounded-full flex-shrink-0 object-cover"
                                             @error="$event.target.src = 'https://ui-avatars.com/api/?name=' + encodeURIComponent(notification.data.user_name || 'User') + '&color=7BC74D&background=EEEEEE&size=64'">

                                        <!-- Content -->
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm text-custom-darkest" x-html="notification.data.message"></p>
                                            <p class="text-xs text-custom-second-darkest mt-1" x-text="notification.time_ago"></p>
                                        </div>

                                        <!-- Unread indicator -->
                                        <div x-show="!notification.read_at" class="flex-shrink-0">
                                            <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Profile dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2">
                        <img class="h-8 w-8 rounded-full" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                    </button>

                    <!-- Profile dropdown menu -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <div class="px-4 py-2 text-sm text-custom-darkest border-b border-custom-second-darkest border-opacity-20">
                            <div class="font-medium"><?php echo e(auth()->user()->name); ?></div>
                            <div class="text-custom-second-darkest"><?php echo e(auth()->user()->email); ?></div>
                        </div>
                        <a href="<?php echo e(route('profile.show')); ?>" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Profile</a>
                        <?php if(auth()->user()->hasManagementAccess()): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Admin Panel</a>
                        <?php endif; ?>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
function notificationDropdown() {
    return {
        open: false,
        loading: false,
        notifications: [],
        unreadCount: 0,
        showPermissionPrompt: false,
        lastLoadTime: null,

        init() {
            this.loadNotifications();
            this.loadUnreadCount();
            this.setupRealTimeUpdates();
        },

        toggleDropdown() {
            this.open = !this.open;
            if (this.open) {
                // Only reload notifications if they haven't been loaded recently (within 30 seconds)
                const now = Date.now();
                if (!this.lastLoadTime || (now - this.lastLoadTime) > 30000) {
                    this.loadNotifications();
                }
                this.checkNotificationPermission();
            }
        },

        checkNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                // Show permission prompt after a short delay
                setTimeout(() => {
                    this.showPermissionPrompt = true;
                }, 1000);
            }
        },

        async loadNotifications() {
            this.loading = true;
            try {
                const response = await fetch('/notifications', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();
                this.notifications = data.notifications;
                this.unreadCount = data.unread_count;
                this.lastLoadTime = Date.now(); // Track when notifications were last loaded
            } catch (error) {
                console.error('Error loading notifications:', error);
            } finally {
                this.loading = false;
            }
        },

        async loadUnreadCount() {
            try {
                const response = await fetch('/notifications/unread-count', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();
                this.unreadCount = data.unread_count;
            } catch (error) {
                console.error('Error loading unread count:', error);
            }
        },

        async markAsRead(notificationId, url = null) {
            try {
                const response = await fetch(`/notifications/${notificationId}/read`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update the notification in the list
                    const notification = this.notifications.find(n => n.id === notificationId);
                    if (notification) {
                        notification.read_at = new Date().toISOString();
                    }
                    this.unreadCount = data.unread_count;

                    // Navigate to the URL if provided
                    if (url) {
                        this.open = false;
                        window.location.href = url;
                    }
                }
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        },

        async markAllAsRead() {
            try {
                const response = await fetch('/notifications/read-all', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Mark all notifications as read in the list
                    this.notifications.forEach(notification => {
                        notification.read_at = new Date().toISOString();
                    });
                    this.unreadCount = 0;
                }
            } catch (error) {
                console.error('Error marking all notifications as read:', error);
            }
        },

        setupRealTimeUpdates() {
            if (window.Echo) {
                window.Echo.private(`App.Models.User.<?php echo e(auth()->id()); ?>`)
                    .notification((notification) => {
                        // Check if notification already exists by ID first
                        const existingById = this.notifications.find(n => n.id === notification.id);

                        if (existingById) {
                            return; // Skip if notification with same ID already exists
                        }

                        // Create a unique key for this notification as secondary check
                        const notificationKey = `${notification.type}-${notification.user_id}-${notification.post_id || ''}-${notification.comment_id || ''}-${notification.group_id || ''}`;

                        // Check if similar notification already exists to prevent duplicates
                        const existingNotification = this.notifications.find(n => {
                            const existingData = n.data || n;
                            const existingKey = `${existingData.type}-${existingData.user_id}-${existingData.post_id || ''}-${existingData.comment_id || ''}-${existingData.group_id || ''}`;

                            // Compare unique keys and check if they're within a reasonable time window
                            return existingKey === notificationKey &&
                                   Math.abs(new Date(n.created_at) - new Date(notification.created_at)) < 30000; // Within 30 seconds
                        });

                        if (!existingNotification) {
                            // Add new notification to the beginning of the list
                            this.notifications.unshift({
                                id: notification.id,
                                type: notification.type,
                                data: notification,
                                read_at: null,
                                created_at: notification.created_at,
                                time_ago: 'just now'
                            });

                            // Update unread count
                            this.unreadCount++;
                        }

                        // Always show browser notification for real-time events
                        this.showBrowserNotification(notification);
                    });
            }
        },

        showBrowserNotification(notification) {
            if (Notification.permission === 'granted') {
                // Prioritize real profile picture, fallback to generated avatar
                const iconUrl = notification.user_avatar ||
                    `https://ui-avatars.com/api/?name=${encodeURIComponent(notification.user_name || 'User')}&color=7BC74D&background=EEEEEE&size=64`;

                const browserNotification = new Notification('UniLink', {
                    body: notification.message.replace(/<[^>]*>/g, ''), // Strip HTML tags
                    icon: iconUrl,
                    badge: iconUrl,
                    tag: `notification-${notification.id}`, // Prevent duplicate notifications
                    requireInteraction: false,
                    silent: false
                });

                // Auto-close after 5 seconds
                setTimeout(() => {
                    browserNotification.close();
                }, 5000);

                // Handle click to navigate to the notification URL
                browserNotification.onclick = () => {
                    window.focus();
                    if (notification.url) {
                        window.location.href = notification.url;
                    }
                    browserNotification.close();
                };
            }
        },

        requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        console.log('Notification permission granted');
                        this.showPermissionPrompt = false;

                        // Show a success message
                        this.showSuccessToast('Browser notifications enabled! You\'ll now receive notifications even when UniLink is closed.');
                    }
                });
            }
        },

        showSuccessToast(message) {
            // Create a temporary toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm';
            toast.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span class="text-sm">${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Remove after 5 seconds
            setTimeout(() => {
                toast.remove();
            }, 5000);
        },

        dismissPermissionPrompt() {
            this.showPermissionPrompt = false;
        }
    }
}
</script>

<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/unilink-header.blade.php ENDPATH**/ ?>