<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Real Group Post Approval Notification\n";
echo "===============================================\n\n";

// Get test data
$student = App\Models\User::where('role', 'student')->first();
$admin = App\Models\User::where('role', 'admin')->first();
$group = App\Models\Group::first();

if (!$student || !$admin || !$group) {
    echo "❌ Missing test data. Need student, admin, and group.\n";
    exit(1);
}

echo "Test Data:\n";
echo "Student (Post Author): {$student->name} (ID: {$student->id})\n";
echo "Admin (Approver): {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Check if the group requires post approval
echo "Group post approval setting: {$group->post_approval}\n";

// Create a test post that needs approval
echo "Creating test post...\n";
$post = App\Models\Post::create([
    'title' => 'Test Post for Real Approval - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'This is a test post to verify real approval notifications.',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "✅ Created post: {$post->title} (ID: {$post->id})\n";
echo "Post approval status: {$post->approval_status}\n\n";

// Count notifications before approval
$notificationsBefore = $student->notifications()->count();
echo "Student notifications before approval: {$notificationsBefore}\n";

// Simulate the actual controller approval process
echo "Simulating real approval process...\n";

// Check permissions (simulate what the controller does)
if (!$group->userCanModerate($admin)) {
    echo "❌ Admin cannot moderate this group\n";
    exit(1);
}

echo "✅ Admin has moderation permissions\n";

// Update post status (simulate controller logic)
$post->update([
    'approval_status' => 'approved',
    'approved_at' => now(),
    'approved_by' => $admin->id,
]);

echo "✅ Post status updated to approved\n";

// Send notification (simulate controller logic)
if ($post->user && $post->user->id !== $admin->id) {
    echo "Sending GroupPostApproved notification...\n";
    
    // Check notification preferences first
    echo "Student wants group_post_approvals: " . ($student->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";
    
    try {
        $post->user->notify(new \App\Notifications\GroupPostApproved($post, $group, $admin));
        echo "✅ Notification sent successfully\n";
    } catch (Exception $e) {
        echo "❌ Error sending notification: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Notification not sent (same user or missing user)\n";
}

// Wait for processing
sleep(2);

// Check notifications after approval
$notificationsAfter = $student->notifications()->count();
echo "Student notifications after approval: {$notificationsAfter}\n";

$increase = $notificationsAfter - $notificationsBefore;
echo "Notification increase: {$increase}\n";

if ($increase > 0) {
    echo "✅ SUCCESS: Approval notification was created!\n";
    
    // Get the latest notification
    $latestNotification = $student->notifications()
        ->where('type', 'App\Notifications\GroupPostApproved')
        ->latest()
        ->first();
    
    if ($latestNotification) {
        echo "\nLatest GroupPostApproved notification:\n";
        echo "- ID: {$latestNotification->id}\n";
        echo "- Type: {$latestNotification->data['type']}\n";
        echo "- Message: {$latestNotification->data['message']}\n";
        echo "- Created: {$latestNotification->created_at}\n";
        echo "- Read: " . ($latestNotification->read_at ? 'YES' : 'NO') . "\n";
    }
} else {
    echo "❌ FAILED: No approval notification was created\n";
    
    // Debug information
    echo "\nDebugging information:\n";
    echo "- Student notifications enabled: " . ($student->notifications_enabled ? 'YES' : 'NO') . "\n";
    echo "- Student notification preferences: " . json_encode($student->notification_preferences) . "\n";
    echo "- Post user ID: {$post->user_id}\n";
    echo "- Admin ID: {$admin->id}\n";
    echo "- Same user check: " . ($post->user->id === $admin->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
}

// Clean up
echo "\nCleaning up test post...\n";
$post->delete();
echo "✅ Test post deleted\n";

echo "\n🏁 Real approval test completed!\n";
