<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Syntax Fix with Authentication\n";
echo "=========================================\n\n";

try {
    // Authenticate a user first
    $user = App\Models\User::find(2); // Jayvee
    auth()->login($user);
    echo "✅ Authenticated as: {$user->name}\n";
    
    // Try to compile and render the view
    $view = view('layouts.unilink-header');
    echo "✅ View compiles successfully!\n";
    
    $html = $view->render();
    echo "✅ View renders successfully!\n";
    echo "HTML length: " . strlen($html) . " characters\n";
    
    // Check if the HTML contains expected elements
    if (strpos($html, 'Notifications') !== false) {
        echo "✅ Notifications section found in HTML\n";
    }
    
    if (strpos($html, $user->name) !== false) {
        echo "✅ User name found in HTML\n";
    }
    
    auth()->logout();
    echo "✅ User logged out\n";
    
} catch (Exception $e) {
    echo "❌ Error:\n";
    echo "Message: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n🏁 Syntax test with auth completed!\n";
