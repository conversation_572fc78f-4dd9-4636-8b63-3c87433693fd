<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Group Post Approval Notifications\n";
echo "==========================================\n\n";

// Get test data
$user = App\Models\User::where('role', 'student')->first();
$admin = App\Models\User::where('role', 'admin')->first();
$group = App\Models\Group::first();

if (!$user || !$admin || !$group) {
    echo "❌ Missing test data. Need at least one student, one admin, and one group.\n";
    exit(1);
}

echo "Test Data:\n";
echo "Post Author: {$user->name} (ID: {$user->id})\n";
echo "Admin/Approver: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Check notification preferences
echo "Checking notification preferences:\n";
echo "User notifications enabled: " . ($user->notifications_enabled ? 'YES' : 'NO') . "\n";
echo "User wants group_post_approvals: " . ($user->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n\n";

// Create a test post that needs approval
echo "Creating test post that needs approval...\n";
$post = App\Models\Post::create([
    'title' => 'Test Post for Approval - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'This is a test post to verify group approval notifications work correctly.',
    'user_id' => $user->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "✅ Created post: {$post->title} (ID: {$post->id})\n";
echo "Approval status: {$post->approval_status}\n\n";

// Count notifications before approval
$notificationsBefore = $user->notifications()->count();
echo "Notifications before approval: {$notificationsBefore}\n";

// Simulate approval by admin
echo "Simulating post approval by admin...\n";
$post->update([
    'approval_status' => 'approved',
    'approved_at' => now(),
    'approved_by' => $admin->id,
]);

// Send the notification manually (simulating what the controller does)
if ($post->user && $post->user->id !== $admin->id) {
    $post->user->notify(new \App\Notifications\GroupPostApproved($post, $group, $admin));
    echo "✅ GroupPostApproved notification sent\n";
} else {
    echo "❌ Notification not sent (same user or missing user)\n";
}

// Wait a moment for processing
sleep(2);

// Check notifications after approval
$notificationsAfter = $user->notifications()->count();
echo "Notifications after approval: {$notificationsAfter}\n";

if ($notificationsAfter > $notificationsBefore) {
    echo "✅ SUCCESS: Notification was created!\n";
    
    // Get the latest notification
    $latestNotification = $user->notifications()
        ->where('type', 'App\Notifications\GroupPostApproved')
        ->latest()
        ->first();
    
    if ($latestNotification) {
        echo "\nLatest GroupPostApproved notification:\n";
        echo "- ID: {$latestNotification->id}\n";
        echo "- Type: {$latestNotification->data['type']}\n";
        echo "- Message: {$latestNotification->data['message']}\n";
        echo "- Created: {$latestNotification->created_at}\n";
        echo "- Read: " . ($latestNotification->read_at ? 'YES' : 'NO') . "\n";
    }
} else {
    echo "❌ FAILED: No notification was created\n";
    
    // Debug information
    echo "\nDebugging information:\n";
    echo "- User ID: {$user->id}\n";
    echo "- Admin ID: {$admin->id}\n";
    echo "- Post User ID: {$post->user_id}\n";
    echo "- Same user check: " . ($post->user->id === $admin->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
}

// Clean up - delete the test post
echo "\nCleaning up test post...\n";
$post->delete();
echo "✅ Test post deleted\n";

echo "\n🏁 Test completed!\n";
