<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Duplicate Notification Fix\n";
echo "=====================================\n\n";

// Get test users
$user1 = App\Models\User::find(2); // Post owner
$user2 = App\Models\User::find(3); // Reactor

echo "Test Users:\n";
echo "User 1 (Post Owner): {$user1->name} (ID: {$user1->id})\n";
echo "User 2 (Reactor): {$user2->name} (ID: {$user2->id})\n\n";

// Create a simple test post
$post = App\Models\Post::create([
    'title' => 'Duplicate Fix Test',
    'content' => 'Testing duplicate notification fix.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Count notifications before
$beforeCount = $user1->notifications()->count();
echo "Notifications before: {$beforeCount}\n";

// Create reaction
$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

echo "Created reaction: {$reaction->type} by {$user2->name}\n";

// Fire the event
echo "Firing PostReactionAdded event...\n";
event(new App\Events\PostReactionAdded($user2, $post, $reaction));

// Wait for processing
sleep(2);

// Check notifications after
$afterCount = $user1->notifications()->count();
echo "Notifications after: {$afterCount}\n";

$increase = $afterCount - $beforeCount;
echo "Increase: {$increase}\n";

if ($increase === 1) {
    echo "✅ SUCCESS: Only 1 notification created (no duplicates)\n";
} elseif ($increase === 0) {
    echo "❌ FAILED: No notification created\n";
} else {
    echo "❌ FAILED: {$increase} notifications created - DUPLICATES DETECTED!\n";
    
    // Show the duplicate notifications
    $recentNotifications = $user1->notifications()
        ->where('type', 'App\Notifications\PostReacted')
        ->where('created_at', '>=', now()->subMinutes(1))
        ->get();
    
    foreach ($recentNotifications as $notification) {
        echo "- {$notification->data['type']}: {$notification->data['message']}\n";
    }
}

// Clean up
echo "\nCleaning up...\n";
$reaction->delete();
$post->delete();
echo "✅ Cleaned up\n";

echo "\n🏁 Duplicate fix test completed!\n";
