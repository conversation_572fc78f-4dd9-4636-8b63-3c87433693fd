<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "📋 Simple Check\n";
echo "===============\n\n";

// Check pending posts
$pendingPosts = App\Models\Post::where('approval_status', 'pending')->count();
echo "Total pending posts: {$pendingPosts}\n";

// Check recent approval notifications
$approvalNotifications = \Illuminate\Support\Facades\DB::table('notifications')
    ->where('type', 'App\Notifications\GroupPostApproved')
    ->where('created_at', '>=', now()->subHours(24))
    ->count();

echo "Approval notifications in last 24h: {$approvalNotifications}\n";

// Check if there's a specific pending post we can test with
$testPost = App\Models\Post::where('approval_status', 'pending')->first();

if ($testPost) {
    echo "Found pending post: {$testPost->title} (ID: {$testPost->id})\n";
    echo "Author: {$testPost->user->name}\n";
    echo "Group: {$testPost->group->name}\n";
} else {
    echo "No pending posts found\n";
}

echo "\nDone!\n";
