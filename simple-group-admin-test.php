<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Simple Group Admin Test\n";
echo "==========================\n\n";

// Get users
$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

echo "Users:\n";
echo "- Arjohn: {$arjohn->name} (ID: {$arjohn->id})\n";
echo "- Jayvee: {$jayvee->name} (ID: {$jayvee->id})\n";
echo "- Group: {$psits->name} (ID: {$psits->id})\n\n";

// Check group admin permissions
echo "Group Admin Check:\n";
echo "- Can Jayvee moderate PSITS? " . ($psits->userCanModerate($jayvee) ? 'YES' : 'NO') . "\n";

// Check Jayvee's role in the group
$membership = \Illuminate\Support\Facades\DB::table('group_members')
    ->where('group_id', $psits->id)
    ->where('user_id', $jayvee->id)
    ->first();

if ($membership) {
    echo "- Jayvee's role in PSITS: {$membership->role}\n";
    echo "- Jayvee's status in PSITS: {$membership->status}\n";
} else {
    echo "- Jayvee is not a member of PSITS\n";
}

// Get Arjohn's pending posts
$pendingPosts = App\Models\Post::where('user_id', $arjohn->id)
    ->where('group_id', $psits->id)
    ->where('approval_status', 'pending')
    ->get();

echo "\nPending posts by Arjohn in PSITS: {$pendingPosts->count()}\n";

if ($pendingPosts->count() > 0) {
    $testPost = $pendingPosts->first();
    echo "Testing with post: \"{$testPost->title}\" (ID: {$testPost->id})\n";
    
    // Count notifications before
    $before = $arjohn->notifications()->count();
    echo "Arjohn's notifications before: {$before}\n";
    
    // Authenticate as Jayvee and approve
    auth()->login($jayvee);
    echo "✅ Authenticated as Jayvee\n";
    
    try {
        $controller = new App\Http\Controllers\GroupController();
        $response = $controller->approvePost($psits, $testPost);
        
        echo "✅ Approval executed\n";
        
        // Check result
        $testPost->refresh();
        echo "Post status: {$testPost->approval_status}\n";
        
        sleep(1);
        
        $after = $arjohn->notifications()->count();
        echo "Arjohn's notifications after: {$after}\n";
        echo "Increase: " . ($after - $before) . "\n";
        
        if (($after - $before) > 0) {
            echo "✅ SUCCESS: Notification created!\n";
        } else {
            echo "❌ FAILED: No notification\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    auth()->logout();
} else {
    echo "No pending posts to test with.\n";
}

echo "\nDone!\n";
