<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing for Duplicate Notifications\n";
echo "======================================\n\n";

// Get test users
$user1 = App\Models\User::where('role', 'student')->first();
$user2 = App\Models\User::where('role', 'student')->skip(1)->first();

if (!$user1 || !$user2) {
    echo "❌ Need at least 2 student users for testing\n";
    exit(1);
}

echo "Test Users:\n";
echo "User 1 (Post Owner): {$user1->name} (ID: {$user1->id})\n";
echo "User 2 (Reactor): {$user2->name} (ID: {$user2->id})\n\n";

// Create a test post
$post = App\Models\Post::create([
    'title' => 'Duplicate Test Post - ' . now()->format('Y-m-d H:i:s'),
    'content' => 'This post is for testing duplicate notifications.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Test 1: Post Reaction
echo "=== TEST 1: POST REACTION ===\n";
$beforeCount = $user1->notifications()->count();
echo "Notifications before reaction: {$beforeCount}\n";

// Create a reaction and fire the event
$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

// Fire the event (this should only create database notifications now, not broadcast)
event(new App\Events\PostReactionAdded($user2, $post, $reaction));

sleep(3); // Wait for processing
$afterCount = $user1->notifications()->count();
echo "Notifications after reaction: {$afterCount}\n";

$expectedIncrease = 1;
$actualIncrease = $afterCount - $beforeCount;

if ($actualIncrease === $expectedIncrease) {
    echo "✅ SUCCESS: Exactly {$expectedIncrease} notification created (no duplicates)\n";
} elseif ($actualIncrease > $expectedIncrease) {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - DUPLICATES DETECTED!\n";
} else {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - NO NOTIFICATION!\n";
}

// Test 2: Post Comment
echo "\n=== TEST 2: POST COMMENT ===\n";
$beforeCount = $user1->notifications()->count();
echo "Notifications before comment: {$beforeCount}\n";

// Create a comment and fire the event
$comment = App\Models\Comment::create([
    'content' => 'Test comment for duplicate testing',
    'user_id' => $user2->id,
    'commentable_type' => App\Models\Post::class,
    'commentable_id' => $post->id,
]);

event(new App\Events\PostCommentAdded($user2, $post, $comment));

sleep(3); // Wait for processing
$afterCount = $user1->notifications()->count();
echo "Notifications after comment: {$afterCount}\n";

$actualIncrease = $afterCount - $beforeCount;

if ($actualIncrease === $expectedIncrease) {
    echo "✅ SUCCESS: Exactly {$expectedIncrease} notification created (no duplicates)\n";
} elseif ($actualIncrease > $expectedIncrease) {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - DUPLICATES DETECTED!\n";
} else {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - NO NOTIFICATION!\n";
}

// Test 3: Comment Reaction
echo "\n=== TEST 3: COMMENT REACTION ===\n";
$beforeCount = $user2->notifications()->count(); // user2 owns the comment
echo "Notifications before comment reaction: {$beforeCount}\n";

// Create a reaction on the comment
$commentReaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Comment::class,
    'reactable_id' => $comment->id,
    'user_id' => $user1->id,
    'type' => 'love',
]);

event(new App\Events\CommentReactionAdded($user1, $comment, $commentReaction));

sleep(3); // Wait for processing
$afterCount = $user2->notifications()->count();
echo "Notifications after comment reaction: {$afterCount}\n";

$actualIncrease = $afterCount - $beforeCount;

if ($actualIncrease === $expectedIncrease) {
    echo "✅ SUCCESS: Exactly {$expectedIncrease} notification created (no duplicates)\n";
} elseif ($actualIncrease > $expectedIncrease) {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - DUPLICATES DETECTED!\n";
} else {
    echo "❌ FAILED: {$actualIncrease} notifications created (expected {$expectedIncrease}) - NO NOTIFICATION!\n";
}

// Show recent notifications for both users
echo "\n=== RECENT NOTIFICATIONS ===\n";
echo "User 1 ({$user1->name}) recent notifications:\n";
$recent1 = $user1->notifications()->latest()->limit(3)->get();
foreach ($recent1 as $notification) {
    $type = str_replace('App\\Notifications\\', '', $notification->type);
    echo "  - {$type}: {$notification->data['message']}\n";
}

echo "\nUser 2 ({$user2->name}) recent notifications:\n";
$recent2 = $user2->notifications()->latest()->limit(3)->get();
foreach ($recent2 as $notification) {
    $type = str_replace('App\\Notifications\\', '', $notification->type);
    echo "  - {$type}: {$notification->data['message']}\n";
}

// Clean up
echo "\nCleaning up...\n";
$commentReaction->delete();
$comment->delete();
$reaction->delete();
$post->delete();
echo "✅ Test data cleaned up\n";

echo "\n🏁 Duplicate notification test completed!\n";
