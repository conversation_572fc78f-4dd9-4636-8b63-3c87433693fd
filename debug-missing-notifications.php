<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Missing Notifications\n";
echo "==================================\n\n";

$arjohn = App\Models\User::find(7); // Arjohn Egamen
$jayvee = App\Models\User::find(2); // Jayvee Tyrone Cordova
$psits = App\Models\Group::find(3); // PSITS group

// Focus on the posts that are missing notifications
$problematicPosts = [72, 73]; // Posts that were approved but have no notifications

foreach ($problematicPosts as $postId) {
    $post = App\Models\Post::find($postId);
    
    if (!$post) {
        echo "❌ Post {$postId} not found\n";
        continue;
    }
    
    echo "🔍 Analyzing Post {$postId}: \"{$post->title}\"\n";
    echo "- Author: {$post->user->name} (ID: {$post->user_id})\n";
    echo "- Group: {$post->group->name} (ID: {$post->group_id})\n";
    echo "- Status: {$post->approval_status}\n";
    echo "- Approved by: {$post->approved_by}\n";
    echo "- Approved at: {$post->approved_at}\n";
    echo "- Created: {$post->created_at}\n";
    
    // Check if notification should have been sent
    $shouldNotify = ($post->user_id !== $post->approved_by) && $post->user;
    echo "- Should notify: " . ($shouldNotify ? 'YES' : 'NO') . "\n";
    
    if ($shouldNotify) {
        // Check user notification preferences
        $wantsNotifications = $post->user->wantsNotification('group_post_approvals');
        echo "- User wants notifications: " . ($wantsNotifications ? 'YES' : 'NO') . "\n";
        
        // Check if notification exists
        $notificationType = $post->approval_status === 'approved' 
            ? 'App\Notifications\GroupPostApproved' 
            : 'App\Notifications\GroupPostRejected';
        
        $notification = $post->user->notifications()
            ->where('type', $notificationType)
            ->whereJsonContains('data->post_id', $post->id)
            ->first();
        
        echo "- Notification exists: " . ($notification ? 'YES' : 'NO') . "\n";
        
        if (!$notification) {
            echo "❌ MISSING NOTIFICATION DETECTED!\n";
            
            // Try to manually trigger the notification to see if it works
            echo "Attempting to manually create notification...\n";
            
            try {
                if ($post->approval_status === 'approved') {
                    $testNotification = new \App\Notifications\GroupPostApproved($post, $psits, $jayvee);
                } else {
                    $testNotification = new \App\Notifications\GroupPostRejected($post, $psits, $jayvee);
                }
                
                // Check notification channels
                $channels = $testNotification->via($post->user);
                echo "- Notification channels: " . implode(', ', $channels) . "\n";
                
                if (in_array('database', $channels)) {
                    // Send the notification
                    $post->user->notify($testNotification);
                    echo "✅ Manual notification sent successfully\n";
                    
                    // Verify it was created
                    $newNotification = $post->user->notifications()
                        ->where('type', $notificationType)
                        ->whereJsonContains('data->post_id', $post->id)
                        ->latest()
                        ->first();
                    
                    if ($newNotification) {
                        echo "✅ Manual notification verified in database\n";
                        echo "- Message: \"{$newNotification->data['message']}\"\n";
                    } else {
                        echo "❌ Manual notification not found in database\n";
                    }
                } else {
                    echo "❌ Database channel not enabled\n";
                }
                
            } catch (Exception $e) {
                echo "❌ Error creating manual notification: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo str_repeat("-", 50) . "\n";
}

// Check if there's a pattern with the approver
echo "\nApprover Analysis:\n";
echo "Posts approved by Admin (ID: 1) with notifications: ";
$adminApprovals = App\Models\Post::where('approved_by', 1)
    ->where('group_id', 3)
    ->where('created_at', '>=', now()->subHours(4))
    ->count();
echo "{$adminApprovals}\n";

echo "Posts approved by Jayvee (ID: 2) with notifications: ";
$jayveeApprovalsWithNotifications = 0;
$jayveeApprovals = App\Models\Post::where('approved_by', 2)
    ->where('group_id', 3)
    ->where('created_at', '>=', now()->subHours(4))
    ->get();

foreach ($jayveeApprovals as $post) {
    $hasNotification = $post->user->notifications()
        ->whereIn('type', ['App\Notifications\GroupPostApproved', 'App\Notifications\GroupPostRejected'])
        ->whereJsonContains('data->post_id', $post->id)
        ->exists();
    
    if ($hasNotification) {
        $jayveeApprovalsWithNotifications++;
    }
}

echo "{$jayveeApprovalsWithNotifications} out of {$jayveeApprovals->count()}\n";

if ($jayveeApprovalsWithNotifications < $jayveeApprovals->count()) {
    echo "❌ Some of Jayvee's approvals are missing notifications!\n";
    
    echo "\nJayvee's approvals without notifications:\n";
    foreach ($jayveeApprovals as $post) {
        $hasNotification = $post->user->notifications()
            ->whereIn('type', ['App\Notifications\GroupPostApproved', 'App\Notifications\GroupPostRejected'])
            ->whereJsonContains('data->post_id', $post->id)
            ->exists();
        
        if (!$hasNotification) {
            echo "- Post {$post->id}: \"{$post->title}\" ({$post->approval_status})\n";
        }
    }
}

echo "\n🏁 Debug completed!\n";
