<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Complete Group Post Approval Flow\n";
echo "============================================\n\n";

// Get test data
$user = App\Models\User::where('role', 'student')->first();
$admin = App\Models\User::where('role', 'admin')->first();
$group = App\Models\Group::first();

echo "Test Data:\n";
echo "Post Author: {$user->name} (ID: {$user->id})\n";
echo "Admin: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Test 1: Approval notification
echo "=== TEST 1: APPROVAL NOTIFICATION ===\n";
$post1 = App\Models\Post::create([
    'title' => 'Test Approval - ' . now()->format('H:i:s'),
    'content' => 'Test content for approval.',
    'user_id' => $user->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

$beforeCount = $user->notifications()->count();
echo "Notifications before: {$beforeCount}\n";

// Simulate the controller approval logic
$post1->update([
    'approval_status' => 'approved',
    'approved_at' => now(),
    'approved_by' => $admin->id,
]);

if ($post1->user && $post1->user->id !== $admin->id) {
    $post1->user->notify(new \App\Notifications\GroupPostApproved($post1, $group, $admin));
}

sleep(2); // Wait for processing
$afterCount = $user->notifications()->count();
echo "Notifications after: {$afterCount}\n";

if ($afterCount > $beforeCount) {
    echo "✅ APPROVAL: Success!\n";
    $latest = $user->notifications()->where('type', 'App\Notifications\GroupPostApproved')->latest()->first();
    if ($latest) {
        echo "   Message: {$latest->data['message']}\n";
    }
} else {
    echo "❌ APPROVAL: Failed!\n";
}

echo "\n";

// Test 2: Rejection notification
echo "=== TEST 2: REJECTION NOTIFICATION ===\n";
$post2 = App\Models\Post::create([
    'title' => 'Test Rejection - ' . now()->format('H:i:s'),
    'content' => 'Test content for rejection.',
    'user_id' => $user->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

$beforeCount = $user->notifications()->count();
echo "Notifications before: {$beforeCount}\n";

// Simulate the controller rejection logic
$post2->update([
    'approval_status' => 'rejected',
    'approved_by' => $admin->id,
]);

if ($post2->user && $post2->user->id !== $admin->id) {
    $post2->user->notify(new \App\Notifications\GroupPostRejected($post2, $group, $admin));
}

sleep(2); // Wait for processing
$afterCount = $user->notifications()->count();
echo "Notifications after: {$afterCount}\n";

if ($afterCount > $beforeCount) {
    echo "✅ REJECTION: Success!\n";
    $latest = $user->notifications()->where('type', 'App\Notifications\GroupPostRejected')->latest()->first();
    if ($latest) {
        echo "   Message: {$latest->data['message']}\n";
    }
} else {
    echo "❌ REJECTION: Failed!\n";
}

// Show recent notifications
echo "\n=== RECENT NOTIFICATIONS ===\n";
$recent = $user->notifications()->latest()->limit(5)->get();
foreach ($recent as $notification) {
    $type = str_replace('App\\Notifications\\', '', $notification->type);
    echo "- {$type}: {$notification->data['message']} ({$notification->created_at->diffForHumans()})\n";
}

// Clean up
echo "\nCleaning up...\n";
$post1->delete();
$post2->delete();
echo "✅ Test posts deleted\n";

echo "\n🏁 Complete test finished!\n";
