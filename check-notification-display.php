<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Checking Notification Display for <PERSON><PERSON><PERSON><PERSON>\n";
echo "===========================================\n\n";

// Get Ar<PERSON>hn's user account
$user = App\Models\User::find(7); // Arjohn Egamen

echo "User: {$user->name} (ID: {$user->id})\n";
echo "Email: {$user->email}\n\n";

// Get all notifications for this user
$allNotifications = $user->notifications()->orderBy('created_at', 'desc')->get();
echo "Total notifications: {$allNotifications->count()}\n\n";

// Get recent notifications (last 24 hours)
$recentNotifications = $user->notifications()
    ->where('created_at', '>=', now()->subHours(24))
    ->orderBy('created_at', 'desc')
    ->get();

echo "Recent notifications (last 24h): {$recentNotifications->count()}\n";
echo str_repeat("-", 60) . "\n";

foreach ($recentNotifications as $notification) {
    echo "ID: {$notification->id}\n";
    echo "Type: {$notification->type}\n";
    echo "Message: \"{$notification->data['message']}\"\n";
    echo "Created: {$notification->created_at}\n";
    echo "Read: " . ($notification->read_at ? 'YES' : 'NO') . "\n";
    echo "Data: " . json_encode($notification->data, JSON_PRETTY_PRINT) . "\n";
    echo str_repeat("-", 60) . "\n";
}

// Check unread count
$unreadCount = $user->unreadNotifications()->count();
echo "\nUnread notifications: {$unreadCount}\n";

// Test the notification API endpoint that the frontend uses
echo "\nTesting notification API endpoint...\n";

// Simulate the API request
$request = new \Illuminate\Http\Request();
$request->headers->set('X-Requested-With', 'XMLHttpRequest');

// Create the controller and call the method
$controller = new App\Http\Controllers\NotificationController();

try {
    // Simulate authentication
    auth()->login($user);
    
    $response = $controller->index($request);
    $responseData = $response->getData(true);
    
    echo "API Response:\n";
    echo "- Notifications count: " . count($responseData['notifications']) . "\n";
    echo "- Unread count: {$responseData['unread_count']}\n";
    
    echo "\nAPI Notifications:\n";
    foreach ($responseData['notifications'] as $index => $notification) {
        if ($index < 5) { // Show first 5
            echo "- {$notification['data']['message']} (Created: {$notification['created_at']})\n";
        }
    }
    
    auth()->logout();
    
} catch (Exception $e) {
    echo "❌ Error testing API: " . $e->getMessage() . "\n";
}

echo "\n🏁 Check completed!\n";
