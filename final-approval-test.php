<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🎯 Final Approval Test\n";
echo "======================\n\n";

$student = App\Models\User::find(2);
$admin = App\Models\User::find(1);
$group = App\Models\Group::find(1);

// Create test post
$post = App\Models\Post::create([
    'title' => 'Final Test',
    'content' => 'Testing',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created post ID: {$post->id}\n";

$before = $student->notifications()->count();
echo "Notifications before: {$before}\n";

// Approve
auth()->login($admin);
$controller = new App\Http\Controllers\GroupController();
$controller->approvePost($group, $post);

$after = $student->notifications()->count();
echo "Notifications after: {$after}\n";
echo "Increase: " . ($after - $before) . "\n";

if (($after - $before) > 0) {
    echo "✅ SUCCESS: Notification created!\n";
} else {
    echo "❌ FAILED: No notification\n";
}

$post->delete();
auth()->logout();
echo "Cleaned up.\n";
