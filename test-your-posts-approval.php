<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Your Actual Posts Approval\n";
echo "=====================================\n\n";

// Get your test posts
$post1 = App\Models\Post::find(65); // test 23
$post2 = App\Models\Post::find(66); // test 24

if (!$post1 || !$post2) {
    echo "❌ Could not find your test posts\n";
    exit(1);
}

$author = $post1->user; // Arjohn Egamen
$group = $post1->group; // PSITS
$admin = App\Models\User::find(1); // Admin user

echo "Test Setup:\n";
echo "- Author: {$author->name} (ID: {$author->id})\n";
echo "- Group: {$group->name} (ID: {$group->id})\n";
echo "- Admin: {$admin->name} (ID: {$admin->id})\n";
echo "- Post 1: \"{$post1->title}\" (ID: {$post1->id})\n";
echo "- Post 2: \"{$post2->title}\" (ID: {$post2->id})\n\n";

// Check group settings
echo "Group Settings:\n";
echo "- Post approval: {$group->post_approval}\n";
echo "- Admin can moderate: " . ($group->userCanModerate($admin) ? 'YES' : 'NO') . "\n\n";

// Check author notification settings
echo "Author Notification Settings:\n";
echo "- Notifications enabled: " . ($author->notifications_enabled ? 'YES' : 'NO') . "\n";
echo "- Wants group_post_approvals: " . ($author->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n\n";

// Count notifications before
$notificationsBefore = $author->notifications()->count();
echo "Author notifications before: {$notificationsBefore}\n\n";

// Test 1: Approve post 1
echo "=== TEST 1: Approving Post 1 ===\n";
echo "Post: \"{$post1->title}\" (ID: {$post1->id})\n";
echo "Current status: {$post1->approval_status}\n";

// Authenticate as admin
auth()->login($admin);
echo "✅ Authenticated as admin\n";

try {
    // Use the actual controller
    $controller = new App\Http\Controllers\GroupController();
    
    echo "Calling approvePost method...\n";
    $response = $controller->approvePost($group, $post1);
    
    echo "✅ Controller method executed\n";
    
    // Check post status
    $post1->refresh();
    echo "New post status: {$post1->approval_status}\n";
    echo "Approved at: {$post1->approved_at}\n";
    echo "Approved by: {$post1->approved_by}\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications
    $notificationsAfter1 = $author->notifications()->count();
    echo "Author notifications after approval: {$notificationsAfter1}\n";
    
    $increase1 = $notificationsAfter1 - $notificationsBefore;
    echo "Notification increase: {$increase1}\n";
    
    if ($increase1 > 0) {
        echo "✅ SUCCESS: Approval notification created!\n";
        
        // Get the notification
        $approvalNotification = $author->notifications()
            ->where('type', 'App\Notifications\GroupPostApproved')
            ->latest()
            ->first();
        
        if ($approvalNotification) {
            echo "Notification message: \"{$approvalNotification->data['message']}\"\n";
        }
    } else {
        echo "❌ FAILED: No approval notification created\n";
        
        // Debug the notification creation
        echo "\nDEBUG INFO:\n";
        echo "- Post user ID: {$post1->user_id}\n";
        echo "- Admin ID: {$admin->id}\n";
        echo "- Same user check: " . ($post1->user_id === $admin->id ? 'YES (SKIP)' : 'NO (SEND)') . "\n";
        
        // Try creating notification manually
        echo "Trying to create notification manually...\n";
        try {
            $notification = new \App\Notifications\GroupPostApproved($post1, $group, $admin);
            $channels = $notification->via($author);
            echo "Notification channels: " . implode(', ', $channels) . "\n";
            
            if (in_array('database', $channels)) {
                $author->notify($notification);
                echo "✅ Manual notification sent\n";
                
                $notificationsAfterManual = $author->notifications()->count();
                echo "Notifications after manual: {$notificationsAfterManual}\n";
            }
        } catch (Exception $e) {
            echo "❌ Manual notification failed: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error in approval process: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 2: Reject post 2
echo "=== TEST 2: Rejecting Post 2 ===\n";
echo "Post: \"{$post2->title}\" (ID: {$post2->id})\n";
echo "Current status: {$post2->approval_status}\n";

$notificationsBefore2 = $author->notifications()->count();

try {
    echo "Calling rejectPost method...\n";
    $response2 = $controller->rejectPost($group, $post2);
    
    echo "✅ Controller method executed\n";
    
    // Check post status
    $post2->refresh();
    echo "New post status: {$post2->approval_status}\n";
    echo "Approved by: {$post2->approved_by}\n";
    
    // Wait for notification processing
    sleep(2);
    
    // Check notifications
    $notificationsAfter2 = $author->notifications()->count();
    echo "Author notifications after rejection: {$notificationsAfter2}\n";
    
    $increase2 = $notificationsAfter2 - $notificationsBefore2;
    echo "Notification increase: {$increase2}\n";
    
    if ($increase2 > 0) {
        echo "✅ SUCCESS: Rejection notification created!\n";
        
        // Get the notification
        $rejectionNotification = $author->notifications()
            ->where('type', 'App\Notifications\GroupPostRejected')
            ->latest()
            ->first();
        
        if ($rejectionNotification) {
            echo "Notification message: \"{$rejectionNotification->data['message']}\"\n";
        }
    } else {
        echo "❌ FAILED: No rejection notification created\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error in rejection process: " . $e->getMessage() . "\n";
}

// Logout
auth()->logout();

echo "\n🏁 Test completed!\n";
