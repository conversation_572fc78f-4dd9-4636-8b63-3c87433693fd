<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Simulating Real Notification...\n\n";

// Get test users
$user1 = App\Models\User::find(1); // Admin
$user2 = App\Models\User::find(3); // Juan <PERSON>
$post = App\Models\Post::first();

if (!$user1 || !$user2 || !$post) {
    echo "❌ Missing test data\n";
    exit;
}

echo "Simulating: {$user1->name} reacting to {$user2->name}'s post\n";
echo "Post: {$post->title}\n";
echo "User1 avatar: " . ($user1->avatar ? '✅ Real photo' : '❌ No photo') . "\n";
echo "User2 avatar: " . ($user2->avatar ? '✅ Real photo' : '❌ No photo') . "\n\n";

// Create or find a reaction
$reaction = App\Models\Reaction::where([
    'reactable_type' => 'App\Models\Post',
    'reactable_id' => $post->id,
    'user_id' => $user1->id,
])->first();

if (!$reaction) {
    $reaction = App\Models\Reaction::create([
        'reactable_type' => 'App\Models\Post',
        'reactable_id' => $post->id,
        'user_id' => $user1->id,
        'type' => 'like'
    ]);
    echo "✅ Created new reaction\n";
} else {
    echo "✅ Using existing reaction\n";
}

// Fire the event (this should trigger both database and broadcast notifications)
echo "🚀 Firing PostReactionAdded event...\n";
event(new App\Events\PostReactionAdded($user1, $post, $reaction));

echo "✅ Event fired successfully!\n\n";

// Check if notification was created
sleep(2); // Give it a moment to process

$latestNotification = App\Models\User::find($post->user_id)
    ->notifications()
    ->where('type', 'App\Notifications\PostReacted')
    ->latest()
    ->first();

if ($latestNotification) {
    $data = $latestNotification->data;
    echo "📬 Latest notification found:\n";
    echo "   Type: {$data['type']}\n";
    echo "   User: {$data['user_name']}\n";
    echo "   Avatar: " . (strpos($data['user_avatar'], 'storage/avatars') !== false ? '✅ Real photo' : '⚠️ Generated') . "\n";
    echo "   Message: {$data['message']}\n";
    echo "   Created: {$latestNotification->created_at}\n";
} else {
    echo "❌ No notification found\n";
}

echo "\n🎯 Simulation Complete!\n";
echo "Check your browser at http://localhost:8000 to see if the notification appears in real-time!\n";
