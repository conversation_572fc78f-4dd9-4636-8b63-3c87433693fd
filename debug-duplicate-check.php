<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🔍 Debugging Duplicate Check Logic\n";
echo "==================================\n\n";

// Get test users
$user1 = App\Models\User::find(2); // Post owner
$user2 = App\Models\User::find(3); // Reactor

echo "Test Users:\n";
echo "User 1 (Post Owner): {$user1->name} (ID: {$user1->id})\n";
echo "User 2 (Reactor): {$user2->name} (ID: {$user2->id})\n\n";

// Create a simple test post
$post = App\Models\Post::create([
    'title' => 'Debug Duplicate Check',
    'content' => 'Testing duplicate check logic.',
    'user_id' => $user1->id,
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Create reaction
$reaction = App\Models\Reaction::create([
    'reactable_type' => App\Models\Post::class,
    'reactable_id' => $post->id,
    'user_id' => $user2->id,
    'type' => 'like',
]);

echo "Created reaction: {$reaction->type} by {$user2->name}\n\n";

// Test the duplicate check logic manually
echo "Testing duplicate check logic:\n";

$existingNotification = $user1->notifications()
    ->where('type', 'App\Notifications\PostReacted')
    ->where('data->user_id', $user2->id)
    ->where('data->post_id', $post->id)
    ->where('data->reaction_type', $reaction->type)
    ->where('created_at', '>=', now()->subMinutes(5))
    ->exists();

echo "Existing notification found: " . ($existingNotification ? 'YES' : 'NO') . "\n";

if (!$existingNotification) {
    echo "No duplicate found, should create notification\n";
    
    // Create notification manually
    $user1->notify(new App\Notifications\PostReacted($user2, $post, $reaction));
    echo "✅ Notification created manually\n";
    
    // Now test the duplicate check again
    $existingNotification2 = $user1->notifications()
        ->where('type', 'App\Notifications\PostReacted')
        ->where('data->user_id', $user2->id)
        ->where('data->post_id', $post->id)
        ->where('data->reaction_type', $reaction->type)
        ->where('created_at', '>=', now()->subMinutes(5))
        ->exists();
    
    echo "After creating notification, duplicate check: " . ($existingNotification2 ? 'YES (FOUND)' : 'NO (NOT FOUND)') . "\n";
    
    // Show the actual notification data
    $latestNotification = $user1->notifications()
        ->where('type', 'App\Notifications\PostReacted')
        ->latest()
        ->first();
    
    if ($latestNotification) {
        echo "\nLatest notification data:\n";
        echo "- Type: {$latestNotification->type}\n";
        echo "- User ID in data: {$latestNotification->data['user_id']}\n";
        echo "- Post ID in data: {$latestNotification->data['post_id']}\n";
        echo "- Reaction type in data: {$latestNotification->data['reaction_type']}\n";
        echo "- Created at: {$latestNotification->created_at}\n";
    }
} else {
    echo "Duplicate found, would skip notification\n";
}

// Clean up
echo "\nCleaning up...\n";
$reaction->delete();
$post->delete();
echo "✅ Cleaned up\n";

echo "\n🏁 Debug completed!\n";
