<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

echo "🧪 Simple Approval Notification Test\n";
echo "====================================\n\n";

// Get test data
$student = App\Models\User::find(2);
$admin = App\Models\User::find(1);
$group = App\Models\Group::find(1);

echo "Test Data:\n";
echo "Student: {$student->name} (ID: {$student->id})\n";
echo "Admin: {$admin->name} (ID: {$admin->id})\n";
echo "Group: {$group->name} (ID: {$group->id})\n\n";

// Create a simple test post
$post = App\Models\Post::create([
    'title' => 'Simple Approval Test',
    'content' => 'Testing approval notification.',
    'user_id' => $student->id,
    'group_id' => $group->id,
    'approval_status' => 'pending',
    'status' => 'published',
    'published_at' => now(),
]);

echo "Created test post: {$post->title} (ID: {$post->id})\n\n";

// Count notifications before
$beforeCount = $student->notifications()->count();
echo "Notifications before: {$beforeCount}\n";

// Test notification preferences
echo "Student wants group_post_approvals: " . ($student->wantsNotification('group_post_approvals') ? 'YES' : 'NO') . "\n";

// Create and send notification directly
echo "Creating GroupPostApproved notification...\n";
try {
    $notification = new App\Notifications\GroupPostApproved($post, $group, $admin);
    echo "✅ Notification object created\n";
    
    // Check via method
    $channels = $notification->via($student);
    echo "Channels: " . implode(', ', $channels) . "\n";
    
    if (in_array('database', $channels)) {
        echo "✅ Database channel enabled\n";
        
        // Send notification
        $student->notify($notification);
        echo "✅ Notification sent\n";
        
        // Check if it was created
        $afterCount = $student->notifications()->count();
        echo "Notifications after: {$afterCount}\n";
        
        $increase = $afterCount - $beforeCount;
        echo "Increase: {$increase}\n";
        
        if ($increase > 0) {
            echo "✅ SUCCESS: Notification created!\n";
        } else {
            echo "❌ FAILED: No notification created\n";
        }
    } else {
        echo "❌ Database channel not enabled\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

// Clean up
echo "\nCleaning up...\n";
$post->delete();
echo "✅ Cleaned up\n";

echo "\n🏁 Simple test completed!\n";
