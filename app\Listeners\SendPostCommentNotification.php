<?php

namespace App\Listeners;

use App\Events\PostCommentAdded;
use App\Notifications\PostCommented;
use App\Notifications\CommentReplied;
class SendPostCommentNotification
{

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostCommentAdded $event): void
    {
        // If this is a reply to another comment
        if ($event->comment->parent_id) {
            $parentComment = $event->comment->parent;
            
            // Don't notify if user replied to their own comment
            if ($event->user->id !== $parentComment->user_id) {
                // Check if the parent comment owner wants to receive this type of notification
                if ($parentComment->user->wantsNotification('comment_replies')) {
                    $parentComment->user->notify(new CommentReplied($event->user, $parentComment, $event->comment));
                }
            }
        } else {
            // This is a direct comment on the post
            // Don't notify if user commented on their own post
            if ($event->user->id !== $event->post->user_id) {
                // Check if the post owner wants to receive this type of notification
                if ($event->post->user->wantsNotification('post_comments')) {
                    // Check for duplicate notifications
                    $existingNotification = $event->post->user->notifications()
                        ->where('type', 'App\Notifications\PostCommented')
                        ->whereJsonContains('data->user_id', $event->user->id)
                        ->whereJsonContains('data->post_id', $event->post->id)
                        ->whereJsonContains('data->comment_id', $event->comment->id)
                        ->where('created_at', '>=', now()->subMinutes(5))
                        ->exists();

                    if (!$existingNotification) {
                        $event->post->user->notify(new PostCommented($event->user, $event->post, $event->comment));
                    }
                }
            }
        }
    }
}
